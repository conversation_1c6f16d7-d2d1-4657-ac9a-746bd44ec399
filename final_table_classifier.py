#!/usr/bin/env python3
"""
最终的表格样式分类器
基于实际观察到的HTML结构差异进行分类
"""

import requests
import time
import json
import re
import logging
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
from bs4 import BeautifulSoup
import pandas as pd


@dataclass
class CrawlConfig:
    """爬虫配置类"""
    api_base_url: str = "http://172.18.151.239:11235"
    api_token: str = "123456"
    max_retries: int = 15
    retry_delay: float = 3.0
    request_timeout: int = 30
    rate_limit_delay: float = 1.0


@dataclass
class CrawlResult:
    """爬取结果数据类"""
    url: str
    success: bool
    html: str = ""
    markdown: str = ""
    cleaned_html: str = ""
    error_message: str = ""
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class LinkExtractor:
    """链接提取器"""
    
    @staticmethod
    def extract_links_from_html(html_content: str, base_url: str) -> List[Dict[str, str]]:
        """从HTML中提取链接"""
        soup = BeautifulSoup(html_content, 'html.parser')
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text(strip=True)
            
            # 处理相对链接
            absolute_url = urljoin(base_url, href)
            
            # 过滤掉无效链接
            if LinkExtractor._is_valid_link(absolute_url, text):
                links.append({
                    'text': text,
                    'url': absolute_url,
                    'original_href': href
                })
        
        return links
    
    @staticmethod
    def _is_valid_link(url: str, text: str) -> bool:
        """验证链接是否有效"""
        if not text or len(text.strip()) < 2:
            return False
        
        if url.startswith(('javascript:', 'mailto:', '#')):
            return False
        
        parsed = urlparse(url)
        if not parsed.scheme in ['http', 'https']:
            return False
        
        return True


class WebCrawler:
    """网页爬虫核心类"""
    
    def __init__(self, config: CrawlConfig):
        self.config = config
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.api_token}"
        }
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('WebCrawler')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def crawl_url(self, url: str) -> CrawlResult:
        """爬取单个URL"""
        self.logger.info(f"开始爬取: {url}")
        
        try:
            task_id = self._submit_crawl_task(url)
            if not task_id:
                return CrawlResult(url=url, success=False, error_message="Failed to submit crawl task")
            
            return self._get_crawl_result(task_id, url)
            
        except Exception as e:
            self.logger.error(f"爬取URL时发生错误 {url}: {e}")
            return CrawlResult(url=url, success=False, error_message=str(e))
    
    def _submit_crawl_task(self, url: str) -> Optional[str]:
        """提交爬取任务"""
        crawl_endpoint = f"{self.config.api_base_url}/crawl"
        payload = {"urls": [url]}
        
        try:
            response = requests.post(
                crawl_endpoint, 
                headers=self.headers, 
                json=payload, 
                timeout=self.config.request_timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("task_id")
            else:
                self.logger.error(f"提交任务失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"网络请求错误: {e}")
            return None
    
    def _get_crawl_result(self, task_id: str, url: str) -> CrawlResult:
        """获取爬取结果"""
        result_endpoint = f"{self.config.api_base_url}/task/{task_id}"
        
        for attempt in range(self.config.max_retries):
            try:
                time.sleep(self.config.retry_delay)
                response = requests.get(
                    result_endpoint, 
                    headers=self.headers, 
                    timeout=self.config.request_timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    status = result.get("status", "")
                    
                    if status == "completed":
                        self.logger.info(f"爬取成功: {url}")
                        return self._parse_completed_result(result, url)
                    elif status == "failed":
                        error_msg = result.get("error", "Unknown error")
                        self.logger.error(f"任务失败: {error_msg}")
                        return CrawlResult(url=url, success=False, error_message=error_msg)
                    else:
                        self.logger.info(f"任务进行中... 状态: {status} (尝试 {attempt + 1}/{self.config.max_retries})")
                        continue
                else:
                    self.logger.error(f"获取结果失败: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"获取结果时网络错误: {e}")
        
        return CrawlResult(url=url, success=False, error_message="Task timeout or failed")
    
    def _parse_completed_result(self, result: Dict, url: str) -> CrawlResult:
        """解析完成的爬取结果"""
        results = result.get("results", [])
        if results and len(results) > 0:
            crawl_data = results[0]
            return CrawlResult(
                url=url,
                success=True,
                html=crawl_data.get("html", ""),
                markdown=crawl_data.get("markdown", ""),
                cleaned_html=crawl_data.get("cleaned_html", "")
            )
        else:
            return CrawlResult(url=url, success=False, error_message="No results in completed task")


class FinalTableClassifier:
    """最终的表格样式分类器"""
    
    def __init__(self):
        self.categories = ["无表格线", "有表格线"]
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('FinalTableClassifier')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def classify(self, crawl_result: CrawlResult) -> str:
        """基于实际观察到的表格结构差异进行分类"""
        if not crawl_result.success:
            return "未分类"
        
        soup = BeautifulSoup(crawl_result.html, 'html.parser')
        
        # 查找包含电话号码的表格
        phone_tables = self._find_phone_tables(soup)
        
        if not phone_tables:
            return "无表格线"
        
        # 分析表格的布局特征
        layout_score = self._calculate_layout_score(phone_tables)
        
        # 根据布局分数进行分类
        if layout_score >= 3:
            classification = "有表格线"
        else:
            classification = "无表格线"
        
        self.logger.info(f"布局分数: {layout_score}, 分类: {classification}")
        return classification
    
    def _find_phone_tables(self, soup) -> List:
        """查找包含电话号码的表格"""
        phone_pattern = r'77\d{5}'
        phone_tables = []
        
        for table in soup.find_all('table'):
            text = table.get_text()
            if re.search(phone_pattern, text):
                # 检查表格是否包含足够的电话号码
                phone_matches = re.findall(phone_pattern, text)
                if len(phone_matches) >= 3:  # 至少包含3个电话号码
                    phone_tables.append(table)
        
        return phone_tables
    
    def _calculate_layout_score(self, tables: List) -> int:
        """计算布局复杂度分数"""
        score = 0
        
        for table in tables:
            table_score = self._analyze_single_table_layout(table)
            score += table_score
        
        return score
    
    def _analyze_single_table_layout(self, table) -> int:
        """分析单个表格的布局复杂度"""
        score = 0
        rows = table.find_all('tr')
        
        if not rows:
            return 0
        
        # 统计多列行的数量和比例
        multi_column_rows = 0
        max_columns = 0
        consistent_multi_column_rows = 0
        
        for row in rows:
            cells = row.find_all(['td', 'th'])
            col_count = len(cells)
            max_columns = max(max_columns, col_count)
            
            if col_count > 2:
                multi_column_rows += 1
                
                # 检查是否为一致的多列布局（3-4列）
                if 3 <= col_count <= 4:
                    consistent_multi_column_rows += 1
        
        total_rows = len(rows)
        multi_column_ratio = multi_column_rows / total_rows if total_rows > 0 else 0
        consistent_ratio = consistent_multi_column_rows / total_rows if total_rows > 0 else 0
        
        # 评分规则（基于实际观察）
        # 1. 如果有一致的3-4列布局，且比例较高，得高分
        if consistent_ratio >= 0.7 and consistent_multi_column_rows >= 3:
            score += 3  # 高分：明显的表格布局
        elif consistent_ratio >= 0.5 and consistent_multi_column_rows >= 2:
            score += 2  # 中分：可能的表格布局
        elif multi_column_ratio >= 0.3 and max_columns >= 4:
            score += 1  # 低分：有一些多列特征
        
        # 2. 检查表格的规整性
        if self._has_regular_structure(rows):
            score += 1
        
        return score
    
    def _has_regular_structure(self, rows) -> bool:
        """检查表格是否有规整的结构"""
        if len(rows) < 3:
            return False
        
        # 检查是否有相似的列数模式
        column_counts = []
        for row in rows:
            cells = row.find_all(['td', 'th'])
            if cells:
                column_counts.append(len(cells))
        
        if not column_counts:
            return False
        
        # 如果大部分行有相同的列数，认为是规整结构
        from collections import Counter
        count_freq = Counter(column_counts)
        most_common_count, most_common_freq = count_freq.most_common(1)[0]
        
        # 如果最常见的列数出现频率超过50%，且列数大于2，认为是规整结构
        if most_common_freq / len(column_counts) >= 0.5 and most_common_count > 2:
            return True
        
        return False
    
    def get_categories(self) -> List[str]:
        """获取所有可能的分类"""
        return self.categories


class FinalTableCrawlerSystem:
    """最终的表格分类爬虫系统主类"""
    
    def __init__(self, config: CrawlConfig = None):
        self.config = config or CrawlConfig()
        self.crawler = WebCrawler(self.config)
        self.classifier = FinalTableClassifier()
        self.link_extractor = LinkExtractor()
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('FinalTableCrawlerSystem')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def extract_and_classify_links(self, source_url: str, output_file: str) -> None:
        """从源URL提取链接并进行最终的表格分类"""
        self.logger.info(f"开始处理源URL: {source_url}")
        
        # 1. 爬取源页面
        source_result = self.crawler.crawl_url(source_url)
        if not source_result.success:
            self.logger.error(f"无法爬取源页面: {source_result.error_message}")
            return
        
        # 2. 提取链接
        links = self.link_extractor.extract_links_from_html(source_result.html, source_url)
        self.logger.info(f"从源页面提取到 {len(links)} 个链接")
        
        if not links:
            self.logger.warning("未找到任何有效链接")
            return
        
        # 3. 爬取和分类每个链接
        results = []
        for i, link in enumerate(links, 1):
            self.logger.info(f"处理第 {i}/{len(links)} 个链接: {link['text']}")
            
            # 爬取链接
            crawl_result = self.crawler.crawl_url(link['url'])
            
            # 分类
            if crawl_result.success:
                classification = self.classifier.classify(crawl_result)
                self.logger.info(f"分类结果: {classification}")
            else:
                classification = "爬取失败"
                self.logger.error(f"爬取失败: {crawl_result.error_message}")
            
            results.append({
                '单位名称': link['text'],
                '子链接': link['url'],
                '分类': classification
            })
            
            # 添加延迟避免过于频繁的请求
            time.sleep(self.config.rate_limit_delay)
        
        # 4. 保存结果
        self._save_results(results, output_file)
        self._print_summary(results)
    
    def _save_results(self, results: List[Dict], output_file: str) -> None:
        """保存结果到CSV文件"""
        try:
            df = pd.DataFrame(results)
            df.to_csv(output_file, index=False, encoding='utf-8')
            self.logger.info(f"结果已保存到: {output_file}")
        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
    
    def _print_summary(self, results: List[Dict]) -> None:
        """打印处理摘要"""
        total = len(results)
        categories = {}
        
        for result in results:
            category = result['分类']
            categories[category] = categories.get(category, 0) + 1
        
        self.logger.info(f"\n📊 最终表格分类摘要:")
        self.logger.info(f"总记录数: {total}")
        for category, count in categories.items():
            percentage = (count / total) * 100 if total > 0 else 0
            self.logger.info(f"  {category}: {count} 个 ({percentage:.1f}%)")


def main():
    """主函数"""
    print("🚀 最终的表格样式分类爬虫系统")
    print("=" * 50)
    print("基于实际HTML结构差异进行精确分类：")
    print("  - 第一类：无表格线（简单列表样式）")
    print("  - 第二类：有表格线（规整的多列表格）")
    print()
    
    # 配置系统
    config = CrawlConfig()
    system = FinalTableCrawlerSystem(config)
    
    # 使用默认参数
    source_url = "http://172.18.1.16/phone/20201227.html"
    output_file = "final_units_data.csv"
    
    print(f"源URL: {source_url}")
    print(f"输出文件: {output_file}")
    print()
    
    # 开始处理
    try:
        system.extract_and_classify_links(source_url, output_file)
        print("\n✅ 处理完成！")
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")


if __name__ == "__main__":
    main()
