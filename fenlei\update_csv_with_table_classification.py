#!/usr/bin/env python3
"""
将表格风格分类结果添加到原始CSV文件
"""
import pandas as pd
import shutil

def update_csv_with_table_classification():
    """
    将表格风格分类结果添加到原始CSV文件
    """
    try:
        # 读取分类结果
        classified_df = pd.read_csv('final_units_data_table_classified.csv', encoding='utf-8')
        print(f"✅ 读取表格风格分类结果: {len(classified_df)} 条记录")
        
        # 重命名分类列为更简洁的名称
        classified_df = classified_df.rename(columns={'表格风格分类': '分类'})
        
        # 备份原始文件
        shutil.copy('final_units_data.csv', 'final_units_data_backup_table.csv')
        print("✅ 已备份原始文件为: final_units_data_backup_table.csv")
        
        # 保存到原始文件
        classified_df.to_csv('final_units_data.csv', index=False, encoding='utf-8')
        print("✅ 已更新原始文件: final_units_data.csv")
        
        # 验证结果
        verify_df = pd.read_csv('final_units_data.csv', encoding='utf-8')
        print(f"✅ 验证结果: {len(verify_df)} 条记录，列名: {list(verify_df.columns)}")
        
        # 显示分类统计
        classification_counts = verify_df['分类'].value_counts()
        print("\n📊 表格风格分类统计:")
        total_count = len(verify_df)
        for category, count in classification_counts.items():
            percentage = (count / total_count) * 100
            print(f"  {category}: {count} 个 ({percentage:.1f}%)")
        
        # 显示一些具体例子
        print(f"\n📋 分类示例:")
        
        simple_examples = verify_df[verify_df['分类'] == '简单表格风格']['单位名称'].head(5).tolist()
        print(f"简单表格风格示例:")
        for example in simple_examples:
            print(f"  - {example}")
        
        complex_examples = verify_df[verify_df['分类'] == '复杂表格风格']['单位名称'].head(5).tolist()
        print(f"\n复杂表格风格示例:")
        for example in complex_examples:
            print(f"  - {example}")
        
        print(f"\n✅ 表格风格分类完成！")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")

def analyze_table_classification_patterns():
    """
    分析表格分类的模式
    """
    try:
        df = pd.read_csv('final_units_data.csv', encoding='utf-8')
        
        print("\n🔍 表格风格分类模式分析:")
        print("=" * 50)
        
        # 按URL路径分析
        print(f"📁 按URL路径分析:")
        
        # 机关部室
        jiguan_df = df[df['子链接'].str.contains('机关部室')]
        print(f"  机关部室: {len(jiguan_df)} 个")
        jiguan_stats = jiguan_df['分类'].value_counts()
        for category, count in jiguan_stats.items():
            print(f"    - {category}: {count} 个")
        
        # 主要生产单位
        shengchan_df = df[df['子链接'].str.contains('主要生产单位')]
        print(f"  主要生产单位: {len(shengchan_df)} 个")
        shengchan_stats = shengchan_df['分类'].value_counts()
        for category, count in shengchan_stats.items():
            print(f"    - {category}: {count} 个")
        
        # 主要经营企业
        jingying_df = df[df['子链接'].str.contains('主要经营企业')]
        print(f"  主要经营企业: {len(jingying_df)} 个")
        jingying_stats = jingying_df['分类'].value_counts()
        for category, count in jingying_stats.items():
            print(f"    - {category}: {count} 个")
        
        # 辅助生产企业
        fuzhu_df = df[df['子链接'].str.contains('辅助生产企业')]
        print(f"  辅助生产企业: {len(fuzhu_df)} 个")
        fuzhu_stats = fuzhu_df['分类'].value_counts()
        for category, count in fuzhu_stats.items():
            print(f"    - {category}: {count} 个")
        
        # 主要服务单位
        fuwu_df = df[df['子链接'].str.contains('主要服务单位')]
        print(f"  主要服务单位: {len(fuwu_df)} 个")
        fuwu_stats = fuwu_df['分类'].value_counts()
        for category, count in fuwu_stats.items():
            print(f"    - {category}: {count} 个")
        
        # 其他
        other_df = df[~df['子链接'].str.contains('机关部室|主要生产单位|主要经营企业|辅助生产企业|主要服务单位')]
        if len(other_df) > 0:
            print(f"  其他: {len(other_df)} 个")
            other_stats = other_df['分类'].value_counts()
            for category, count in other_stats.items():
                print(f"    - {category}: {count} 个")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    update_csv_with_table_classification()
    analyze_table_classification_patterns()
