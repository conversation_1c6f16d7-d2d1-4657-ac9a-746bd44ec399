#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的有表格线页面提取功能
完全按照fully_universal_crawler.py的逻辑实现
"""

import asyncio
import csv
from datetime import datetime
from base_crawler import BaseCrawler, CrawlConfig
from phone_extractor import PhoneExtractor
from csv_processor import CSVProcessor


async def test_specific_table_unit():
    """测试特定的有表格线单位（采矿场）"""
    print("🎯 测试特定有表格线单位：采矿场")
    print("=" * 60)
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    
    # 直接测试采矿场（fully_universal_crawler.py验证过的成功案例）
    test_url = "http://172.18.1.16/phone/主要生产单位/采矿场/ckc.html"
    print(f"🌐 测试URL: {test_url}")
    
    html_content = await crawler.crawl_url(test_url)
    if not html_content:
        print("❌ 页面爬取失败")
        return []
    
    print(f"✅ 页面爬取成功，内容长度: {len(html_content)}")
    
    # 使用修复后的提取器
    records = extractor.extract_phone_info(html_content, "采矿场", "有表格线")
    
    print(f"✅ 采矿场提取到 {len(records)} 条电话记录:")
    for i, record in enumerate(records[:10], 1):  # 只显示前10条
        print(f"  {i:2d}. {record.phone} - {record.name} ({record.unit}) [{record.extraction_method}]")
    
    if len(records) > 10:
        print(f"  ... 还有 {len(records) - 10} 条记录")
    
    return records


async def test_all_table_style_units():
    """测试所有有表格线的单位"""
    print(f"\n🎯 测试所有有表格线单位")
    print("=" * 60)
    
    # 加载CSV数据
    csv_processor = CSVProcessor()
    if not csv_processor.load_data():
        print("❌ CSV数据加载失败")
        return []
    
    # 获取所有有表格线的单位
    table_style_units = []
    for record in csv_processor.iter_records_by_classification("有表格线"):
        table_style_units.append(record)
    
    print(f"📊 找到 {len(table_style_units)} 个有表格线的单位")
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    all_records = []
    
    for i, record in enumerate(table_style_units, 1):
        print(f"\n📋 处理第 {i}/{len(table_style_units)} 个单位: {record.unit_name}")
        print(f"  🔗 链接: {record.sub_link}")
        
        try:
            # 爬取页面
            html_content = await crawler.crawl_url(record.sub_link)
            if not html_content:
                print(f"❌ 爬取失败: {record.unit_name}")
                continue
            
            print(f"  ✅ 页面爬取成功，内容长度: {len(html_content)}")
            
            # 使用修复后的提取器
            phone_records = extractor.extract_phone_info(
                html_content,
                record.unit_name,
                record.classification
            )
            
            if phone_records:
                all_records.extend(phone_records)
                print(f"  ✅ 提取到 {len(phone_records)} 条电话记录")
                
                # 显示前3条记录
                for j, phone_record in enumerate(phone_records[:3], 1):
                    print(f"    {j}. {phone_record.phone} - {phone_record.name} ({phone_record.unit})")
                if len(phone_records) > 3:
                    print(f"    ... 还有 {len(phone_records) - 3} 条记录")
            else:
                print(f"  ⚠️ 未提取到电话信息")
            
            # 添加延迟
            await asyncio.sleep(2)
            
        except Exception as e:
            print(f"❌ 处理异常: {e}")
    
    return all_records


def save_table_test_results(records):
    """保存有表格线测试结果"""
    print(f"\n🎯 保存有表格线测试结果")
    print("=" * 60)
    
    if not records:
        print("⚠️ 没有数据可保存")
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存CSV文件
    csv_filename = f"有表格线测试结果_{timestamp}.csv"
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['电话号码', '单位名称', '电话用户名', '提取方法']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in records:
                writer.writerow({
                    '电话号码': record.phone,
                    '单位名称': record.unit,
                    '电话用户名': record.name,
                    '提取方法': record.extraction_method
                })
        
        print(f"✅ CSV文件保存成功: {csv_filename}")
    except Exception as e:
        print(f"❌ CSV文件保存失败: {e}")
    
    # 显示统计信息
    print(f"\n📊 有表格线测试结果统计:")
    print(f"  总电话数: {len(records)} 条")
    
    # 按单位统计
    unit_stats = {}
    for record in records:
        unit = record.unit.split('-')[0]  # 取基础单位名称
        if unit not in unit_stats:
            unit_stats[unit] = 0
        unit_stats[unit] += 1
    
    print(f"  按单位统计:")
    for unit, count in sorted(unit_stats.items()):
        print(f"    {unit}: {count} 条")
    
    # 检查重复号码
    phone_count = {}
    for record in records:
        phone = record.phone
        if phone not in phone_count:
            phone_count[phone] = 0
        phone_count[phone] += 1
    
    duplicate_phones = [phone for phone, count in phone_count.items() if count > 1]
    if duplicate_phones:
        print(f"  重复号码: {len(duplicate_phones)} 个")
        for phone in duplicate_phones[:5]:  # 只显示前5个
            print(f"    {phone} (出现 {phone_count[phone]} 次)")
        if len(duplicate_phones) > 5:
            print(f"    ... 还有 {len(duplicate_phones) - 5} 个重复号码")
    
    # 按提取方法统计
    method_stats = {}
    for record in records:
        method = record.extraction_method
        if method not in method_stats:
            method_stats[method] = 0
        method_stats[method] += 1
    
    print(f"  按提取方法统计:")
    for method, count in method_stats.items():
        print(f"    {method}: {count} 条")


async def main():
    """主测试函数"""
    print("🕷️ 有表格线页面提取功能测试")
    print("基于fully_universal_crawler.py的完全通用逻辑")
    print("=" * 70)
    
    try:
        # 测试1: 特定单位（采矿场）
        specific_records = await test_specific_table_unit()
        
        # 测试2: 所有有表格线单位
        all_records = await test_all_table_style_units()
        
        # 合并结果
        total_records = specific_records + all_records
        
        # 保存测试结果
        save_table_test_results(total_records)
        
        print(f"\n🎉 有表格线测试完成！")
        print("=" * 70)
        print("✅ 测试结果:")
        print(f"  - 采矿场单独测试: {len(specific_records)} 条记录")
        print(f"  - 所有有表格线单位: {len(all_records)} 条记录")
        print(f"  - 总计: {len(total_records)} 条记录")
        print("✅ 修复验证:")
        print(f"  - fully_universal_crawler.py逻辑集成: {'✅ 成功' if total_records else '❌ 失败'}")
        print(f"  - 有表格线页面提取: {'✅ 修复成功' if total_records else '❌ 仍有问题'}")
        print(f"  - 复杂表格结构支持: {'✅ 支持' if total_records else '❌ 不支持'}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
