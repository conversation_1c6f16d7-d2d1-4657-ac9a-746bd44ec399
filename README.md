# 模块化电话信息爬虫系统

基于crawl4ai的通用电话信息提取工具，支持有表格线和无表格线两种网页样式的电话信息爬取。

## 🚀 功能特点

- **模块化设计**：采用模块化架构，易于维护和扩展
- **通用性强**：支持有表格线和无表格线两种网页样式
- **智能提取**：自动检测页面类型并选择合适的提取方法
- **错误处理**：完善的错误处理和重试机制
- **日志记录**：详细的日志记录，便于调试和监控
- **数据导出**：支持CSV和JSON格式的数据导出

## 📁 项目结构

```
├── base_crawler.py          # 基础爬虫模块
├── phone_extractor.py       # 电话信息提取器
├── csv_processor.py         # CSV数据处理器
├── main_crawler.py          # 主程序入口（完整版）
├── demo_crawler.py          # 演示程序（快速验证）
├── test_crawler.py          # 测试程序
├── final_units_data.csv     # 单位数据文件
└── README.md               # 使用说明
```

## 🛠️ 环境要求

- Python 3.7+
- 本地部署的crawl4ai服务（地址：**************:11235）
- 依赖包：
  - aiohttp
  - beautifulsoup4
  - pandas
  - asyncio

## 📦 安装依赖

```bash
pip install aiohttp beautifulsoup4 pandas
```

## 🎯 使用方法

### 1. 快速演示（推荐）

运行演示程序，快速验证系统功能：

```bash
python demo_crawler.py
```

演示程序将：
- 爬取主页面电话信息
- 处理3个样本单位
- 生成演示结果文件

### 2. 完整爬取

运行主程序，处理所有单位：

```bash
python main_crawler.py
```

主程序将：
- 爬取主页面电话信息
- 逐行处理CSV文件中的所有42个单位
- 生成完整的结果文件和统计信息

### 3. 功能测试

运行测试程序，验证各模块功能：

```bash
python test_crawler.py
```

## 📊 输出文件

### CSV格式
```csv
电话号码,单位名称,电话用户名,提取方法
7719080,行政工作部,部长,文本模式
7719081,行政工作部,副部长,文本模式
```

### JSON格式
```json
[
  {
    "电话号码": "7719080",
    "单位名称": "行政工作部",
    "电话用户名": "部长",
    "提取方法": "文本模式"
  }
]
```

## 🔧 模块说明

### 1. base_crawler.py - 基础爬虫模块

提供统一的crawl4ai接口：
- 连接测试
- 网页爬取（支持同步和异步模式）
- 错误处理和重试机制
- 日志记录

```python
from base_crawler import BaseCrawler

crawler = BaseCrawler()
html = await crawler.crawl_url("http://example.com")
```

### 2. phone_extractor.py - 电话信息提取器

支持两种网页样式的电话信息提取：
- **有表格线**：使用表格结构提取
- **无表格线**：使用文本模式提取
- **自动检测**：自动判断页面类型

```python
from phone_extractor import PhoneExtractor

extractor = PhoneExtractor()
records = extractor.extract_phone_info(html, "单位名称", "auto")
```

### 3. csv_processor.py - CSV数据处理器

处理final_units_data.csv文件：
- 读取和验证CSV数据
- 按分类字段判断处理方式
- 提供迭代器接口

```python
from csv_processor import CSVProcessor

processor = CSVProcessor()
processor.load_data()
for record in processor.iter_all_records():
    print(record.unit_name, record.classification)
```

## 📋 CSV数据格式

final_units_data.csv文件格式：

```csv
单位名称,子链接,分类
行政工作部,http://172.18.1.16/phone/机关部室/xzgzb.html,无表格线
采矿场,http://172.18.1.16/phone/生产单位/cmc.html,有表格线
```

分类说明：
- **无表格线**：使用文本模式提取电话信息
- **有表格线**：使用表格模式提取电话信息
- **爬取失败**：跳过处理

## 🔍 提取逻辑

### 无表格线页面（文本模式）
1. 分行处理HTML内容
2. 清理HTML标签
3. 使用正则表达式查找电话号码
4. 在电话号码前查找中文姓名
5. 验证电话号码和姓名的有效性

### 有表格线页面（表格模式）
1. 查找所有带边框的表格
2. 动态获取表格上方的单位名称
3. 检查表格是否包含电话信息
4. 从表格单元格中提取电话和姓名
5. 处理多层单位结构

## 📝 日志记录

程序运行时会生成详细的日志文件：
- 文件名格式：`main_crawler_YYYYMMDD_HHMMSS.log`
- 记录爬取过程、提取结果、错误信息等

## ⚠️ 注意事项

1. **crawl4ai服务**：确保本地crawl4ai服务正常运行
2. **网络连接**：确保能够访问目标网站
3. **请求频率**：程序内置延迟机制，避免请求过快
4. **数据验证**：建议运行前先执行测试程序验证功能

## 🎉 运行示例

成功运行演示程序的输出示例：

```
🕷️ 模块化电话信息爬虫系统 - 演示版
============================================================
🎯 步骤1: 爬取主页面电话信息
✅ 主页面提取到 2 条电话记录

🎯 步骤2: 爬取样本单位电话信息
📊 CSV数据统计:
  总单位数: 42
  无表格线: 25 个单位
  有表格线: 16 个单位

🎯 步骤3: 保存演示结果
📊 演示结果统计:
  总电话数: 27 条
  按单位统计:
    矿领导: 2 条
    行政工作部: 14 条
    矿纪委: 6 条
    风控内审部: 5 条

🎉 演示完成！
```

## 🤝 技术支持

如有问题，请检查：
1. crawl4ai服务是否正常运行
2. 网络连接是否正常
3. 依赖包是否正确安装
4. CSV文件格式是否正确

## 📄 许可证

本项目仅供学习和研究使用。
