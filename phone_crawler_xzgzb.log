2025-06-12 18:20:42,099 - ERROR - 读取CSV文件失败: [Errno 2] No such file or directory: 'final_units_data.csv'
2025-06-12 18:21:45,349 - INFO - 成功读取CSV文件: final_units_data.csv
2025-06-12 18:21:45,350 - INFO - CSV文件包含 41 行数据
2025-06-12 18:21:45,351 - INFO - 找到目标单位: 行政工作部
2025-06-12 18:21:45,351 - INFO - 子链接: http://172.18.1.16/phone/机关部室/xzgzb.html
2025-06-12 18:21:45,352 - INFO - 开始使用crawl4ai爬取: http://172.18.1.16/phone/机关部室/xzgzb.html
2025-06-12 18:21:45,361 - INFO - ✅ crawl4ai服务连接成功
2025-06-12 18:21:45,361 - INFO - 尝试异步任务方式...
2025-06-12 18:21:45,377 - INFO - API响应键: ['task_id']
2025-06-12 18:21:45,379 - INFO - ✅ 异步任务提交成功，任务ID: af1d35fb-9c64-447a-9c65-b79477279c5e
2025-06-12 18:21:45,379 - INFO - 检查任务状态 (1/30)...
2025-06-12 18:21:45,395 - INFO - 任务状态: pending
2025-06-12 18:21:47,396 - INFO - 检查任务状态 (2/30)...
2025-06-12 18:21:47,403 - INFO - 任务状态: completed
2025-06-12 18:21:47,404 - INFO - ✅ 任务完成，获取结果...
2025-06-12 18:21:47,404 - INFO - ✅ 获取HTML内容成功，长度: 2374 字符
2025-06-12 18:21:47,405 - INFO - 开始提取电话信息...
2025-06-12 18:21:47,408 - INFO - ✅ 提取到: 7719080 - 部长
2025-06-12 18:21:47,408 - INFO - ✅ 提取到: 7719081 - 副部长
2025-06-12 18:21:47,410 - INFO - ✅ 提取到: 7716397 - 副部长
2025-06-12 18:21:47,411 - INFO - ✅ 提取到: 7716306 - 秘书
2025-06-12 18:21:47,411 - INFO - ✅ 提取到: 7716076 - 秘书
2025-06-12 18:21:47,411 - INFO - ✅ 提取到: 7719179 - 外事
2025-06-12 18:21:47,412 - INFO - ✅ 提取到: 7719880 - 传真
2025-06-12 18:21:47,412 - INFO - ✅ 提取到: 7717576 - 大楼维修组
2025-06-12 18:21:47,412 - INFO - ✅ 提取到: 7717574 - 收发室
2025-06-12 18:21:47,412 - INFO - ✅ 提取到: 7716500 - 大楼门卫
2025-06-12 18:21:47,413 - INFO - ✅ 提取到: 7719083 - 小车队队长办
2025-06-12 18:21:47,413 - INFO - ✅ 提取到: 7719084 - 小车队值班室
2025-06-12 18:21:47,413 - INFO - ✅ 提取到: 7717579 - 小车队排班室
2025-06-12 18:21:47,413 - INFO - ✅ 提取到: 7719086 - 小车队统计室
2025-06-12 18:21:47,413 - INFO - 共提取到 14 条电话记录
2025-06-12 18:21:47,420 - INFO - ✅ 数据已保存:
2025-06-12 18:21:47,420 - INFO -    - 行政工作部_电话信息.csv
2025-06-12 18:21:47,420 - INFO -    - 行政工作部_电话信息.json
2025-07-04 17:04:13,911 - INFO - 成功读取CSV文件: final_units_data.csv
2025-07-04 17:04:13,912 - INFO - CSV文件包含 41 行数据
2025-07-04 17:04:13,913 - INFO - 找到目标单位: 行政工作部
2025-07-04 17:04:13,914 - INFO - 子链接: http://172.18.1.16/phone/机关部室/xzgzb.html
2025-07-04 17:04:13,914 - INFO - 开始使用crawl4ai爬取: http://172.18.1.16/phone/机关部室/xzgzb.html
2025-07-04 17:04:13,932 - INFO - ✅ crawl4ai服务连接成功
2025-07-04 17:04:13,933 - INFO - 尝试异步任务方式...
2025-07-04 17:04:13,942 - INFO - API响应键: ['task_id']
2025-07-04 17:04:13,943 - INFO - ✅ 异步任务提交成功，任务ID: 4c1099c3-4829-4a76-8d7a-5c50938d3d82
2025-07-04 17:04:13,943 - INFO - 检查任务状态 (1/30)...
2025-07-04 17:04:13,960 - INFO - 任务状态: pending
2025-07-04 17:04:15,962 - INFO - 检查任务状态 (2/30)...
2025-07-04 17:04:15,978 - INFO - 任务状态: completed
2025-07-04 17:04:15,979 - INFO - ✅ 任务完成，获取结果...
2025-07-04 17:04:15,979 - INFO - ✅ 获取HTML内容成功，长度: 2373 字符
2025-07-04 17:04:15,980 - INFO - 开始提取电话信息...
2025-07-04 17:04:15,986 - INFO - ✅ 提取到: 7719080 - 部长
2025-07-04 17:04:15,986 - INFO - ✅ 提取到: 7719081 - 副部长
2025-07-04 17:04:15,987 - INFO - ✅ 提取到: 7716397 - 副部长
2025-07-04 17:04:15,987 - INFO - ✅ 提取到: 7716306 - 秘书
2025-07-04 17:04:15,990 - INFO - ✅ 提取到: 7716076 - 秘书
2025-07-04 17:04:15,990 - INFO - ✅ 提取到: 7719179 - 外事
2025-07-04 17:04:15,990 - INFO - ✅ 提取到: 7719880 - 传真
2025-07-04 17:04:15,991 - INFO - ✅ 提取到: 7717576 - 大楼维修组
2025-07-04 17:04:15,992 - INFO - ✅ 提取到: 7717574 - 收发室
2025-07-04 17:04:16,002 - INFO - ✅ 提取到: 7716500 - 大楼门卫
2025-07-04 17:04:16,004 - INFO - ✅ 提取到: 7719083 - 小车队队长办
2025-07-04 17:04:16,005 - INFO - ✅ 提取到: 7719084 - 小车队值班室
2025-07-04 17:04:16,005 - INFO - ✅ 提取到: 7717579 - 小车队排班室
2025-07-04 17:04:16,006 - INFO - ✅ 提取到: 7719086 - 小车队统计室
2025-07-04 17:04:16,006 - INFO - 共提取到 14 条电话记录
2025-07-04 17:04:16,008 - INFO - ✅ 数据已保存:
2025-07-04 17:04:16,008 - INFO -    - 行政工作部_电话信息.csv
2025-07-04 17:04:16,008 - INFO -    - 行政工作部_电话信息.json
2025-07-07 17:32:57,865 - INFO - 成功读取CSV文件: final_units_data.csv
2025-07-07 17:32:57,865 - INFO - CSV文件包含 42 行数据
2025-07-07 17:32:57,867 - INFO - 找到目标单位: 行政工作部
2025-07-07 17:32:57,868 - INFO - 子链接: http://172.18.1.16/phone/机关部室/xzgzb.html
2025-07-07 17:32:57,869 - INFO - 开始使用crawl4ai爬取: http://172.18.1.16/phone/机关部室/xzgzb.html
2025-07-07 17:32:57,880 - INFO - ✅ crawl4ai服务连接成功
2025-07-07 17:32:57,880 - INFO - 尝试异步任务方式...
2025-07-07 17:32:57,885 - INFO - API响应键: ['task_id']
2025-07-07 17:32:57,887 - INFO - ✅ 异步任务提交成功，任务ID: 3cc33447-00c3-4726-bc86-3c235a30347d
2025-07-07 17:32:57,889 - INFO - 检查任务状态 (1/30)...
2025-07-07 17:32:57,904 - INFO - 任务状态: pending
2025-07-07 17:32:59,906 - INFO - 检查任务状态 (2/30)...
2025-07-07 17:32:59,923 - INFO - 任务状态: completed
2025-07-07 17:32:59,924 - INFO - ✅ 任务完成，获取结果...
2025-07-07 17:32:59,924 - INFO - ✅ 获取HTML内容成功，长度: 2373 字符
2025-07-07 17:32:59,925 - INFO - 开始提取电话信息...
2025-07-07 17:32:59,929 - INFO - ✅ 提取到: 7719080 - 部长
2025-07-07 17:32:59,931 - INFO - ✅ 提取到: 7719081 - 副部长
2025-07-07 17:32:59,931 - INFO - ✅ 提取到: 7716397 - 副部长
2025-07-07 17:32:59,932 - INFO - ✅ 提取到: 7716306 - 秘书
2025-07-07 17:32:59,932 - INFO - ✅ 提取到: 7716076 - 秘书
2025-07-07 17:32:59,932 - INFO - ✅ 提取到: 7719179 - 外事
2025-07-07 17:32:59,934 - INFO - ✅ 提取到: 7719880 - 传真
2025-07-07 17:32:59,934 - INFO - ✅ 提取到: 7717576 - 大楼维修组
2025-07-07 17:32:59,936 - INFO - ✅ 提取到: 7717574 - 收发室
2025-07-07 17:32:59,945 - INFO - ✅ 提取到: 7716500 - 大楼门卫
2025-07-07 17:32:59,945 - INFO - ✅ 提取到: 7719083 - 小车队队长办
2025-07-07 17:32:59,946 - INFO - ✅ 提取到: 7719084 - 小车队值班室
2025-07-07 17:32:59,946 - INFO - ✅ 提取到: 7717579 - 小车队排班室
2025-07-07 17:32:59,946 - INFO - ✅ 提取到: 7719086 - 小车队统计室
2025-07-07 17:32:59,947 - INFO - 共提取到 14 条电话记录
2025-07-07 17:32:59,949 - INFO - ✅ 数据已保存:
2025-07-07 17:32:59,950 - INFO -    - 行政工作部_电话信息.csv
2025-07-07 17:32:59,951 - INFO -    - 行政工作部_电话信息.json
