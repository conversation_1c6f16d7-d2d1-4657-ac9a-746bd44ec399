# 表格边框样式分类爬虫系统

## 🎯 项目概述

这是一个专门用于爬取网页并根据HTML表格的边框样式进行分类的智能爬虫系统。系统基于Crawl4AI进行网页爬取，并通过分析HTML表格结构来区分两种不同的表格样式：

- **第一类：无表格线** - 简单的列表样式布局
- **第二类：有表格线** - 复杂的多列表格布局

## 🏗️ 系统架构

### 核心组件

1. **WebCrawler** - 网页爬虫核心类
   - 使用Crawl4AI API进行网页爬取
   - 支持异步任务处理和重试机制
   - 自动处理网络错误和超时

2. **FinalTableClassifier** - 表格样式分类器
   - 基于HTML表格结构进行智能分析
   - 计算布局复杂度分数
   - 区分简单列表和复杂表格布局

3. **LinkExtractor** - 链接提取器
   - 从HTML中提取有效链接
   - 处理相对链接转换
   - 过滤无效和重复链接

4. **FinalTableCrawlerSystem** - 系统主控制器
   - 协调各组件工作
   - 管理爬取流程
   - 生成分类报告

### 🧠 分类算法

系统通过以下特征来判断表格样式：

#### 无表格线特征：
- 主要为单列或双列布局
- 简单的列表式排列
- 电话号码以垂直列表形式显示
- 布局复杂度分数 < 3

#### 有表格线特征：
- 规整的3-4列表格布局
- 多行多列的数据结构
- 一致的表格行列模式
- 布局复杂度分数 ≥ 3

## 🚀 使用方法

### 环境要求

1. **Python 3.7+**
2. **依赖包**：
   ```bash
   pip install requests beautifulsoup4 pandas
   ```

3. **Crawl4AI服务**：
   ```bash
   docker run --rm -it -e CRAWL4AI_API_TOKEN=123456 -p 11235:11235 unclecode/crawl4ai:all-amd64
   ```

### 快速开始

1. **运行自动分类**：
   ```bash
   python final_table_classifier.py
   ```

2. **自定义配置**：
   ```python
   from final_table_classifier import CrawlConfig, FinalTableCrawlerSystem
   
   # 配置爬虫参数
   config = CrawlConfig(
       api_base_url="http://**************:11235",
       api_token="123456",
       max_retries=15,
       retry_delay=3.0
   )
   
   # 创建系统实例
   system = FinalTableCrawlerSystem(config)
   
   # 执行分类
   system.extract_and_classify_links(
       source_url="http://***********/phone/20201227.html",
       output_file="final_units_data.csv"
   )
   ```

### 输出格式

系统生成CSV文件，包含以下列：
- **单位名称** - 链接的文本描述
- **子链接** - 完整的URL地址
- **分类** - "无表格线" 或 "有表格线"

## 📊 分类结果示例

根据实际运行结果，系统成功分类了42个网页：

### 无表格线 (26个, 61.9%)
- 行政工作部、矿纪委、风控内审部、对外联络部
- 企业管理部、党委工作部、工会工作部、财务管理部
- 供应销售部、机动能源部、党委组织部、矿团委
- 环境保护部、科技质量部、生产运营部、党委宣传部
- 人力资源部、安全防尘部、工程管理部、计划发展部
- 数字化部、检化中心、地测中心、信息档案中心
- （德兴）项目经理部办公电话、4G专网手机号码

### 有表格线 (16个, 38.1%)
- 采矿场、泗洲选矿厂、大山选矿厂、精尾综合厂
- 百泰公司、尾矿回收厂、化工公司、新技术厂
- 江铜集团（德兴）铸造有限公司、江铜集团（德兴）建设有限公司
- 江铜集团（德兴）实业有限公司、运输部、动力厂
- 保卫部、后勤服务中心、德铜宾馆

## ⚙️ 配置参数

```python
@dataclass
class CrawlConfig:
    api_base_url: str = "http://**************:11235"  # Crawl4AI API地址
    api_token: str = "123456"                          # API认证令牌
    max_retries: int = 15                              # 最大重试次数
    retry_delay: float = 3.0                           # 重试延迟(秒)
    request_timeout: int = 30                          # 请求超时(秒)
    rate_limit_delay: float = 1.0                      # 请求间隔(秒)
```

## 📁 文件说明

- `final_table_classifier.py` - 主要的分类系统
- `final_units_data.csv` - 最终分类结果
- `deep_content_analysis.py` - 深度内容分析工具
- `manual_sample_analysis.py` - 手动样本分析工具
- `analyze_sample_pages.py` - 样本页面分析工具

## ✨ 技术特点

### 1. 模块化设计
- 各组件职责清晰，易于维护和扩展
- 支持配置化参数调整
- 面向对象的设计模式

### 2. 智能分类算法
- 基于实际HTML结构分析
- 多维度特征评估
- 自适应阈值调整

### 3. 健壮性
- 完善的错误处理机制
- 网络重试和超时控制
- 详细的日志记录

### 4. 通用性
- 不依赖特定网站结构
- 可适配不同的表格样式
- 易于扩展到其他分类场景

## 🔧 扩展性

系统设计支持以下扩展：

1. **新的分类策略** - 继承ClassificationStrategy基类
2. **不同的数据源** - 修改LinkExtractor实现
3. **多种输出格式** - 扩展结果保存方法
4. **自定义特征** - 调整分类算法参数

## ⚠️ 注意事项

1. 确保Crawl4AI服务正常运行
2. 网络连接稳定，避免频繁超时
3. 根据目标网站调整请求间隔
4. 定期检查分类准确性并调优算法

## 👨‍💻 作者信息

本系统由AI助手开发，专门用于基于HTML表格样式的网页分类任务。系统具有良好的通用性和扩展性，可适用于类似的网页分析场景。

---

**🎉 任务完成！** 系统已成功实现基于表格边框样式的网页分类功能。
