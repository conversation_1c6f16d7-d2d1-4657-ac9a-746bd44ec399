#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试表格结构程序
分析有表格线页面的实际HTML结构
"""

import asyncio
from base_crawler import BaseCrawler
from bs4 import BeautifulSoup


async def debug_table_structure():
    """调试表格结构"""
    print("🔍 调试有表格线页面的HTML结构")
    print("=" * 60)
    
    crawler = BaseCrawler()
    
    # 测试采矿场页面
    test_url = "http://172.18.1.16/phone/主要生产单位/采矿场/ckc.html"
    print(f"🌐 测试URL: {test_url}")
    
    html_content = await crawler.crawl_url(test_url)
    if not html_content:
        print("❌ 页面爬取失败")
        return
    
    print(f"✅ 页面爬取成功，内容长度: {len(html_content)}")
    
    # 解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找所有表格
    all_tables = soup.find_all('table')
    print(f"\n📋 总共找到 {len(all_tables)} 个表格")
    
    # 分析每个表格的属性
    for i, table in enumerate(all_tables, 1):
        print(f"\n表格 {i}:")
        print(f"  属性: {table.attrs}")
        
        # 检查边框属性
        border = table.get('border')
        print(f"  border属性: {border}")
        
        # 检查样式
        style = table.get('style')
        print(f"  style属性: {style}")
        
        # 检查class
        class_attr = table.get('class')
        print(f"  class属性: {class_attr}")
        
        # 获取表格内容预览
        table_text = table.get_text(strip=True)[:200]
        print(f"  内容预览: {table_text}...")
        
        # 检查是否包含电话号码
        import re
        phones = re.findall(r'\b\d{7,11}\b', table_text)
        print(f"  包含电话号码: {len(phones)} 个")
        if phones:
            print(f"    示例: {phones[:3]}")
    
    # 查找带边框的表格（不同的查找方式）
    print(f"\n🔍 使用不同方式查找带边框的表格:")
    
    # 方式1: border="1"
    tables_border_1 = soup.find_all('table', {'border': '1'})
    print(f"  border='1': {len(tables_border_1)} 个")
    
    # 方式2: border存在且不为0
    tables_with_border = soup.find_all('table', border=True)
    print(f"  有border属性: {len(tables_with_border)} 个")
    
    # 方式3: 样式中包含border
    tables_style_border = []
    for table in all_tables:
        style = table.get('style', '')
        if 'border' in style.lower():
            tables_style_border.append(table)
    print(f"  样式中有border: {len(tables_style_border)} 个")
    
    # 方式4: 查找所有可能的表格
    print(f"\n📊 详细分析所有表格:")
    for i, table in enumerate(all_tables, 1):
        rows = table.find_all('tr')
        cells_count = sum(len(row.find_all(['td', 'th'])) for row in rows)
        
        print(f"  表格{i}: {len(rows)}行, {cells_count}个单元格")
        
        # 检查是否包含电话信息
        table_text = table.get_text()
        phone_count = len(re.findall(r'\b\d{7,11}\b', table_text))
        if phone_count > 0:
            print(f"    ✅ 包含 {phone_count} 个电话号码")
            
            # 显示表格的前几行
            for j, row in enumerate(rows[:3], 1):
                cells = row.find_all(['td', 'th'])
                cell_texts = [cell.get_text(strip=True) for cell in cells]
                print(f"      行{j}: {cell_texts}")
        else:
            print(f"    ❌ 不包含电话号码")


if __name__ == "__main__":
    asyncio.run(debug_table_structure())
