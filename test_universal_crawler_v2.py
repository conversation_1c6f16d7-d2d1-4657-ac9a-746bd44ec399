#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用电话爬虫方法二测试脚本
对比方法二与标准程序的结果，验证正确性
"""

import pandas as pd
import os
import sys
from datetime import datetime
from universal_phone_crawler_v2 import UniversalPhoneCrawler
import logging


def compare_with_standard():
    """
    与标准程序进行详细对比
    """
    print("🧪 开始测试通用电话爬虫方法二...")
    print("=" * 60)
    
    # 创建爬虫实例
    crawler = UniversalPhoneCrawler(log_level=logging.INFO)
    
    # 测试采矿场数据
    unit_name = "采矿场"
    print(f"🎯 测试单位: {unit_name}")
    
    # 运行方法二
    print("\n📊 运行方法二...")
    v2_data = crawler.crawl_unit_phones(unit_name)
    
    if not v2_data:
        print("❌ 方法二未获取到数据")
        return
    
    # 保存方法二的结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    v2_filename = f"{unit_name}_phone_data_v2_{timestamp}.csv"
    crawler.save_to_csv(v2_data, v2_filename)
    
    # 运行标准程序
    print("\n📊 运行标准程序...")
    try:
        import subprocess
        result = subprocess.run(['python', 'fully_universal_crawler.py'], 
                              capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            print("✅ 标准程序运行成功")
            
            # 查找标准程序的输出文件
            standard_files = [f for f in os.listdir('.') if f.startswith(f'{unit_name}_phone_data_') and f.endswith('.csv') and 'v2' not in f]
            if standard_files:
                standard_file = max(standard_files, key=lambda x: os.path.getctime(x))  # 获取最新的文件
                standard_df = pd.read_csv(standard_file, encoding='utf-8')
                standard_data = standard_df.to_dict('records')
                
                print(f"✅ 标准程序数据加载成功: {len(standard_data)} 条记录")
                
                # 详细对比
                compare_results(v2_data, standard_data, v2_filename, standard_file)
                
            else:
                print("❌ 未找到标准程序的输出文件")
        else:
            print(f"❌ 标准程序运行失败: {result.stderr}")
            
    except Exception as e:
        print(f"⚠️ 无法运行标准程序: {e}")
        
        # 如果无法运行标准程序，只验证方法二的数据质量
        print("\n📊 仅验证方法二数据质量...")
        validation_result = crawler.validate_data(v2_data)
        
        print(f"\n✅ 方法二测试完成！")
        print(f"📊 共获取 {len(v2_data)} 条记录")
        print(f"💾 数据已保存到: {v2_filename}")


def compare_results(v2_data, standard_data, v2_file, standard_file):
    """
    详细对比两个数据集
    """
    print("\n" + "=" * 60)
    print("📊 详细对比结果")
    print("=" * 60)
    
    # 基本统计
    print(f"📋 数据量对比:")
    print(f"  方法二: {len(v2_data)} 条记录")
    print(f"  标准程序: {len(standard_data)} 条记录")
    print(f"  差异: {abs(len(v2_data) - len(standard_data))} 条")
    
    if len(v2_data) == len(standard_data):
        print("✅ 数据量完全匹配！")
    else:
        print("⚠️ 数据量存在差异")
    
    # 转换为便于对比的格式
    v2_phones = set(record['电话号码'] for record in v2_data)
    standard_phones = set(record['电话号码'] for record in standard_data)
    
    # 电话号码对比
    print(f"\n📞 电话号码对比:")
    print(f"  方法二独有: {len(v2_phones - standard_phones)} 个")
    print(f"  标准程序独有: {len(standard_phones - v2_phones)} 个")
    print(f"  共同拥有: {len(v2_phones & standard_phones)} 个")
    
    # 显示差异详情
    if v2_phones - standard_phones:
        print(f"\n🔍 方法二独有的电话号码:")
        for phone in sorted(list(v2_phones - standard_phones)[:10]):
            v2_record = next(r for r in v2_data if r['电话号码'] == phone)
            print(f"  {phone} | {v2_record['电话用户名']} | {v2_record['单位名称']}")
        if len(v2_phones - standard_phones) > 10:
            print(f"  ... 还有 {len(v2_phones - standard_phones) - 10} 个")
    
    if standard_phones - v2_phones:
        print(f"\n🔍 标准程序独有的电话号码:")
        for phone in sorted(list(standard_phones - v2_phones)[:10]):
            standard_record = next(r for r in standard_data if r['电话号码'] == phone)
            print(f"  {phone} | {standard_record['电话用户名']} | {standard_record['单位名称']}")
        if len(standard_phones - v2_phones) > 10:
            print(f"  ... 还有 {len(standard_phones - v2_phones) - 10} 个")
    
    # 完全匹配的记录对比
    exact_matches = 0
    user_name_diffs = 0
    unit_name_diffs = 0
    
    for v2_record in v2_data:
        phone = v2_record['电话号码']
        # 查找标准程序中相同电话号码的记录
        standard_record = next((r for r in standard_data if r['电话号码'] == phone), None)
        
        if standard_record:
            if (v2_record['电话用户名'] == standard_record['电话用户名'] and
                v2_record['单位名称'] == standard_record['单位名称']):
                exact_matches += 1
            else:
                if v2_record['电话用户名'] != standard_record['电话用户名']:
                    user_name_diffs += 1
                if v2_record['单位名称'] != standard_record['单位名称']:
                    unit_name_diffs += 1
    
    print(f"\n🎯 记录匹配度:")
    print(f"  完全匹配: {exact_matches} 条")
    print(f"  用户名不同: {user_name_diffs} 条")
    print(f"  单位名称不同: {unit_name_diffs} 条")
    
    if exact_matches == len(v2_data) == len(standard_data):
        print("🎉 完美匹配！方法二与标准程序结果完全一致！")
    else:
        match_rate = exact_matches / max(len(v2_data), len(standard_data)) * 100
        print(f"📊 匹配率: {match_rate:.1f}%")
    
    # 生成对比报告
    generate_comparison_report(v2_data, standard_data, v2_file, standard_file, 
                             exact_matches, user_name_diffs, unit_name_diffs)


def generate_comparison_report(v2_data, standard_data, v2_file, standard_file,
                             exact_matches, user_name_diffs, unit_name_diffs):
    """
    生成详细的对比报告
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"comparison_report_v2_{timestamp}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("通用电话爬虫方法二对比报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"方法二文件: {v2_file}\n")
        f.write(f"标准程序文件: {standard_file}\n\n")
        
        f.write("数据量统计:\n")
        f.write(f"  方法二: {len(v2_data)} 条记录\n")
        f.write(f"  标准程序: {len(standard_data)} 条记录\n")
        f.write(f"  差异: {abs(len(v2_data) - len(standard_data))} 条\n\n")
        
        f.write("匹配度统计:\n")
        f.write(f"  完全匹配: {exact_matches} 条\n")
        f.write(f"  用户名不同: {user_name_diffs} 条\n")
        f.write(f"  单位名称不同: {unit_name_diffs} 条\n")
        
        if exact_matches == len(v2_data) == len(standard_data):
            f.write("\n🎉 结论: 完美匹配！方法二与标准程序结果完全一致！\n")
        else:
            match_rate = exact_matches / max(len(v2_data), len(standard_data)) * 100
            f.write(f"\n📊 匹配率: {match_rate:.1f}%\n")
            f.write("⚠️ 结论: 存在差异，需要进一步优化\n")
        
        # 详细差异列表
        f.write("\n" + "=" * 50 + "\n")
        f.write("详细差异分析:\n")
        f.write("=" * 50 + "\n")
        
        # 用户名不同的记录
        if user_name_diffs > 0:
            f.write("\n用户名不同的记录:\n")
            f.write("-" * 30 + "\n")
            for v2_record in v2_data:
                phone = v2_record['电话号码']
                standard_record = next((r for r in standard_data if r['电话号码'] == phone), None)
                if (standard_record and 
                    v2_record['电话用户名'] != standard_record['电话用户名']):
                    f.write(f"电话: {phone}\n")
                    f.write(f"  方法二: {v2_record['电话用户名']}\n")
                    f.write(f"  标准程序: {standard_record['电话用户名']}\n")
                    f.write(f"  单位: {v2_record['单位名称']}\n\n")
        
        # 单位名称不同的记录
        if unit_name_diffs > 0:
            f.write("\n单位名称不同的记录:\n")
            f.write("-" * 30 + "\n")
            for v2_record in v2_data:
                phone = v2_record['电话号码']
                standard_record = next((r for r in standard_data if r['电话号码'] == phone), None)
                if (standard_record and 
                    v2_record['单位名称'] != standard_record['单位名称']):
                    f.write(f"电话: {phone}\n")
                    f.write(f"  用户: {v2_record['电话用户名']}\n")
                    f.write(f"  方法二单位: {v2_record['单位名称']}\n")
                    f.write(f"  标准程序单位: {standard_record['单位名称']}\n\n")
    
    print(f"\n📄 详细对比报告已保存到: {report_file}")


if __name__ == "__main__":
    compare_with_standard()
