#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV数据处理器模块
负责读取和处理final_units_data.csv文件
"""

import pandas as pd
import logging
from typing import List, Dict, Optional, Iterator
from dataclasses import dataclass


@dataclass
class UnitRecord:
    """单位记录数据类"""
    unit_name: str
    sub_link: str
    classification: str
    row_index: int


class CSVProcessor:
    """
    CSV数据处理器
    
    功能：
    1. 读取final_units_data.csv文件
    2. 解析单位信息
    3. 按分类字段判断处理方式
    4. 提供迭代器接口逐行处理
    """
    
    def __init__(self, csv_file: str = "final_units_data.csv"):
        """
        初始化CSV处理器
        
        Args:
            csv_file: CSV文件路径
        """
        self.csv_file = csv_file
        self.logger = self._setup_logger()
        self.data = None
        self.total_rows = 0
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def load_data(self) -> bool:
        """
        加载CSV数据

        Returns:
            bool: 加载成功返回True，失败返回False
        """
        try:
            self.logger.info(f"📂 开始加载CSV文件: {self.csv_file}")

            # 尝试不同的编码方式读取CSV文件
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']

            for encoding in encodings:
                try:
                    self.data = pd.read_csv(self.csv_file, encoding=encoding)
                    self.total_rows = len(self.data)
                    self.logger.info(f"✅ 使用 {encoding} 编码成功读取CSV文件")
                    break
                except UnicodeDecodeError:
                    continue
            else:
                self.logger.error("❌ 尝试所有编码都失败")
                return False
            
            self.logger.info(f"✅ CSV文件加载成功")
            self.logger.info(f"📊 总行数: {self.total_rows}")
            self.logger.info(f"📋 列名: {list(self.data.columns)}")
            
            # 验证必要的列是否存在
            required_columns = ['单位名称', '子链接', '分类']
            missing_columns = [col for col in required_columns if col not in self.data.columns]
            
            if missing_columns:
                self.logger.error(f"❌ 缺少必要的列: {missing_columns}")
                return False
            
            # 显示分类统计
            self._show_classification_stats()
            
            return True
            
        except FileNotFoundError:
            self.logger.error(f"❌ 文件不存在: {self.csv_file}")
            return False
        except Exception as e:
            self.logger.error(f"❌ 加载CSV文件失败: {e}")
            return False
    
    def _show_classification_stats(self):
        """显示分类统计信息"""
        if self.data is not None and '分类' in self.data.columns:
            classification_counts = self.data['分类'].value_counts()
            self.logger.info("📈 分类统计:")
            for classification, count in classification_counts.items():
                self.logger.info(f"   {classification}: {count} 条")
    
    def get_unit_record(self, unit_name: str) -> Optional[UnitRecord]:
        """
        根据单位名称获取单位记录
        
        Args:
            unit_name: 单位名称
            
        Returns:
            Optional[UnitRecord]: 找到返回记录，否则返回None
        """
        if self.data is None:
            self.logger.error("❌ 数据未加载，请先调用load_data()")
            return None
        
        try:
            # 查找匹配的行
            target_rows = self.data[self.data['单位名称'] == unit_name]
            
            if target_rows.empty:
                self.logger.warning(f"⚠️ 未找到单位: {unit_name}")
                return None
            
            # 获取第一个匹配的行
            row = target_rows.iloc[0]
            row_index = target_rows.index[0]
            
            record = UnitRecord(
                unit_name=row['单位名称'],
                sub_link=row['子链接'],
                classification=row['分类'],
                row_index=row_index
            )
            
            self.logger.info(f"🎯 找到单位记录: {unit_name} -> {record.sub_link} ({record.classification})")
            return record
            
        except Exception as e:
            self.logger.error(f"❌ 获取单位记录失败: {e}")
            return None
    
    def iter_all_records(self) -> Iterator[UnitRecord]:
        """
        迭代所有单位记录
        
        Yields:
            UnitRecord: 单位记录
        """
        if self.data is None:
            self.logger.error("❌ 数据未加载，请先调用load_data()")
            return
        
        self.logger.info(f"🔄 开始迭代所有记录，共 {self.total_rows} 条")
        
        for index, row in self.data.iterrows():
            try:
                record = UnitRecord(
                    unit_name=row['单位名称'],
                    sub_link=row['子链接'],
                    classification=row['分类'],
                    row_index=index
                )
                
                self.logger.info(f"📋 处理第 {index + 1}/{self.total_rows} 条: {record.unit_name} ({record.classification})")
                yield record
                
            except Exception as e:
                self.logger.error(f"❌ 处理第 {index + 1} 行时出错: {e}")
                continue
    
    def iter_records_by_classification(self, classification: str) -> Iterator[UnitRecord]:
        """
        按分类迭代记录
        
        Args:
            classification: 分类名称（"有表格线" 或 "无表格线"）
            
        Yields:
            UnitRecord: 匹配分类的单位记录
        """
        if self.data is None:
            self.logger.error("❌ 数据未加载，请先调用load_data()")
            return
        
        # 筛选指定分类的记录
        filtered_data = self.data[self.data['分类'] == classification]
        filtered_count = len(filtered_data)
        
        self.logger.info(f"🔍 筛选分类 '{classification}' 的记录，共 {filtered_count} 条")
        
        for index, row in filtered_data.iterrows():
            try:
                record = UnitRecord(
                    unit_name=row['单位名称'],
                    sub_link=row['子链接'],
                    classification=row['分类'],
                    row_index=index
                )
                
                self.logger.info(f"📋 处理 {record.unit_name}")
                yield record
                
            except Exception as e:
                self.logger.error(f"❌ 处理记录时出错: {e}")
                continue
    
    def get_classification_counts(self) -> Dict[str, int]:
        """
        获取各分类的数量统计
        
        Returns:
            Dict[str, int]: 分类名称到数量的映射
        """
        if self.data is None:
            self.logger.error("❌ 数据未加载，请先调用load_data()")
            return {}
        
        if '分类' not in self.data.columns:
            self.logger.error("❌ 数据中没有'分类'列")
            return {}
        
        return self.data['分类'].value_counts().to_dict()
    
    def validate_data(self) -> bool:
        """
        验证数据完整性
        
        Returns:
            bool: 数据有效返回True，否则返回False
        """
        if self.data is None:
            self.logger.error("❌ 数据未加载")
            return False
        
        issues = []
        
        # 检查空值
        for column in ['单位名称', '子链接', '分类']:
            null_count = self.data[column].isnull().sum()
            if null_count > 0:
                issues.append(f"'{column}' 列有 {null_count} 个空值")
        
        # 检查重复的单位名称
        duplicate_units = self.data[self.data['单位名称'].duplicated()]
        if not duplicate_units.empty:
            issues.append(f"发现 {len(duplicate_units)} 个重复的单位名称")
        
        # 检查无效的URL
        invalid_urls = self.data[~self.data['子链接'].str.startswith('http')]
        if not invalid_urls.empty:
            issues.append(f"发现 {len(invalid_urls)} 个无效的URL")
        
        # 检查分类值
        valid_classifications = ['有表格线', '无表格线']
        invalid_classifications = self.data[~self.data['分类'].isin(valid_classifications)]
        if not invalid_classifications.empty:
            issues.append(f"发现 {len(invalid_classifications)} 个无效的分类值")
        
        if issues:
            self.logger.warning("⚠️ 数据验证发现问题:")
            for issue in issues:
                self.logger.warning(f"   - {issue}")
            return False
        else:
            self.logger.info("✅ 数据验证通过")
            return True
    
    def get_summary(self) -> Dict[str, any]:
        """
        获取数据摘要信息
        
        Returns:
            Dict[str, any]: 摘要信息
        """
        if self.data is None:
            return {"error": "数据未加载"}
        
        summary = {
            "total_rows": self.total_rows,
            "columns": list(self.data.columns),
            "classification_counts": self.get_classification_counts(),
            "sample_records": []
        }
        
        # 添加样本记录（前3条）
        for index, row in self.data.head(3).iterrows():
            summary["sample_records"].append({
                "unit_name": row['单位名称'],
                "sub_link": row['子链接'],
                "classification": row['分类']
            })
        
        return summary


# 便捷函数
def load_csv_simple(csv_file: str = "final_units_data.csv") -> Optional[CSVProcessor]:
    """
    简单的CSV加载函数
    
    Args:
        csv_file: CSV文件路径
        
    Returns:
        Optional[CSVProcessor]: 成功返回处理器实例，失败返回None
    """
    processor = CSVProcessor(csv_file)
    if processor.load_data():
        return processor
    return None


if __name__ == "__main__":
    # 测试代码
    processor = CSVProcessor()
    
    if processor.load_data():
        print("CSV文件加载成功")
        
        # 显示摘要
        summary = processor.get_summary()
        print(f"总行数: {summary['total_rows']}")
        print(f"分类统计: {summary['classification_counts']}")
        
        # 验证数据
        processor.validate_data()
        
        # 测试获取特定单位
        record = processor.get_unit_record("行政工作部")
        if record:
            print(f"找到记录: {record.unit_name} -> {record.sub_link}")
        
        # 测试迭代前3条记录
        print("\n前3条记录:")
        count = 0
        for record in processor.iter_all_records():
            print(f"  {record.unit_name} ({record.classification})")
            count += 1
            if count >= 3:
                break
    else:
        print("CSV文件加载失败")
