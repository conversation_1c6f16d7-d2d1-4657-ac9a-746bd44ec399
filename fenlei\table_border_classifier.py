#!/usr/bin/env python3
"""
基于表格边框样式的网页分类系统
Table Border Style Classification System

专门用于检测HTML表格是否有边框线，并据此进行分类
"""

import requests
import time
import json
import re
import logging
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
from bs4 import BeautifulSoup
import pandas as pd


@dataclass
class CrawlConfig:
    """爬虫配置类"""
    api_base_url: str = "http://172.18.151.239:11235"
    api_token: str = "123456"
    max_retries: int = 15
    retry_delay: float = 3.0
    request_timeout: int = 30
    rate_limit_delay: float = 1.0


@dataclass
class CrawlResult:
    """爬取结果数据类"""
    url: str
    success: bool
    html: str = ""
    markdown: str = ""
    cleaned_html: str = ""
    error_message: str = ""
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class LinkExtractor:
    """链接提取器"""
    
    @staticmethod
    def extract_links_from_html(html_content: str, base_url: str) -> List[Dict[str, str]]:
        """从HTML中提取链接"""
        soup = BeautifulSoup(html_content, 'html.parser')
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text(strip=True)
            
            # 处理相对链接
            absolute_url = urljoin(base_url, href)
            
            # 过滤掉无效链接
            if LinkExtractor._is_valid_link(absolute_url, text):
                links.append({
                    'text': text,
                    'url': absolute_url,
                    'original_href': href
                })
        
        return links
    
    @staticmethod
    def _is_valid_link(url: str, text: str) -> bool:
        """验证链接是否有效"""
        if not text or len(text.strip()) < 2:
            return False
        
        if url.startswith(('javascript:', 'mailto:', '#')):
            return False
        
        parsed = urlparse(url)
        if not parsed.scheme in ['http', 'https']:
            return False
        
        return True


class WebCrawler:
    """网页爬虫核心类"""
    
    def __init__(self, config: CrawlConfig):
        self.config = config
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.api_token}"
        }
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('WebCrawler')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def crawl_url(self, url: str) -> CrawlResult:
        """爬取单个URL"""
        self.logger.info(f"开始爬取: {url}")
        
        try:
            task_id = self._submit_crawl_task(url)
            if not task_id:
                return CrawlResult(url=url, success=False, error_message="Failed to submit crawl task")
            
            return self._get_crawl_result(task_id, url)
            
        except Exception as e:
            self.logger.error(f"爬取URL时发生错误 {url}: {e}")
            return CrawlResult(url=url, success=False, error_message=str(e))
    
    def _submit_crawl_task(self, url: str) -> Optional[str]:
        """提交爬取任务"""
        crawl_endpoint = f"{self.config.api_base_url}/crawl"
        payload = {"urls": [url]}
        
        try:
            response = requests.post(
                crawl_endpoint, 
                headers=self.headers, 
                json=payload, 
                timeout=self.config.request_timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("task_id")
            else:
                self.logger.error(f"提交任务失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"网络请求错误: {e}")
            return None
    
    def _get_crawl_result(self, task_id: str, url: str) -> CrawlResult:
        """获取爬取结果"""
        result_endpoint = f"{self.config.api_base_url}/task/{task_id}"
        
        for attempt in range(self.config.max_retries):
            try:
                time.sleep(self.config.retry_delay)
                response = requests.get(
                    result_endpoint, 
                    headers=self.headers, 
                    timeout=self.config.request_timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    status = result.get("status", "")
                    
                    if status == "completed":
                        self.logger.info(f"爬取成功: {url}")
                        return self._parse_completed_result(result, url)
                    elif status == "failed":
                        error_msg = result.get("error", "Unknown error")
                        self.logger.error(f"任务失败: {error_msg}")
                        return CrawlResult(url=url, success=False, error_message=error_msg)
                    else:
                        self.logger.info(f"任务进行中... 状态: {status} (尝试 {attempt + 1}/{self.config.max_retries})")
                        continue
                else:
                    self.logger.error(f"获取结果失败: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"获取结果时网络错误: {e}")
        
        return CrawlResult(url=url, success=False, error_message="Task timeout or failed")
    
    def _parse_completed_result(self, result: Dict, url: str) -> CrawlResult:
        """解析完成的爬取结果"""
        results = result.get("results", [])
        if results and len(results) > 0:
            crawl_data = results[0]
            return CrawlResult(
                url=url,
                success=True,
                html=crawl_data.get("html", ""),
                markdown=crawl_data.get("markdown", ""),
                cleaned_html=crawl_data.get("cleaned_html", "")
            )
        else:
            return CrawlResult(url=url, success=False, error_message="No results in completed task")


class TableBorderClassifier:
    """基于表格边框样式的分类器"""
    
    def __init__(self):
        self.categories = ["无表格线", "有表格线"]
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('TableBorderClassifier')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def classify(self, crawl_result: CrawlResult) -> str:
        """基于表格边框样式进行分类"""
        if not crawl_result.success:
            return "未分类"
        
        soup = BeautifulSoup(crawl_result.html, 'html.parser')
        
        # 查找所有表格
        tables = soup.find_all('table')
        
        if not tables:
            self.logger.info("未找到表格，默认分类为无表格线")
            return "无表格线"
        
        has_border_count = 0
        no_border_count = 0
        
        for table in tables:
            if self._has_table_border(table):
                has_border_count += 1
                self.logger.debug(f"发现有边框的表格")
            else:
                no_border_count += 1
                self.logger.debug(f"发现无边框的表格")
        
        # 根据表格边框情况决定分类
        if has_border_count > no_border_count:
            classification = "有表格线"
        else:
            classification = "无表格线"
        
        self.logger.info(f"表格分析: 有边框={has_border_count}, 无边框={no_border_count}, 分类={classification}")
        return classification
    
    def _has_table_border(self, table) -> bool:
        """检测表格是否有边框"""
        # 1. 检查table元素的border属性
        border_attr = table.get('border')
        if border_attr and str(border_attr) != '0':
            return True
        
        # 2. 检查table元素的style属性中的border
        style_attr = table.get('style', '')
        if self._has_border_in_style(style_attr):
            return True
        
        # 3. 检查table元素的class属性（可能包含边框样式）
        class_attr = table.get('class', [])
        if isinstance(class_attr, list):
            class_str = ' '.join(class_attr)
        else:
            class_str = str(class_attr)
        
        if 'border' in class_str.lower():
            return True
        
        # 4. 检查表格单元格的边框
        cells = table.find_all(['td', 'th'])
        border_cell_count = 0
        
        for cell in cells[:10]:  # 只检查前10个单元格以提高性能
            cell_style = cell.get('style', '')
            if self._has_border_in_style(cell_style):
                border_cell_count += 1
        
        # 如果超过一半的单元格有边框，认为表格有边框
        if len(cells) > 0 and border_cell_count > len(cells) * 0.3:
            return True
        
        # 5. 检查CSS样式表中的表格样式
        style_tags = table.find_parent().find_all('style') if table.find_parent() else []
        for style_tag in style_tags:
            if style_tag.string and 'table' in style_tag.string and 'border' in style_tag.string:
                # 简单检查CSS中是否有表格边框定义
                css_content = style_tag.string.lower()
                if re.search(r'table.*border\s*:\s*[^0\s]', css_content) or \
                   re.search(r'td.*border\s*:\s*[^0\s]', css_content) or \
                   re.search(r'th.*border\s*:\s*[^0\s]', css_content):
                    return True
        
        return False
    
    def _has_border_in_style(self, style_str: str) -> bool:
        """检查样式字符串中是否包含边框定义"""
        if not style_str:
            return False
        
        style_lower = style_str.lower()
        
        # 检查各种边框样式
        border_patterns = [
            r'border\s*:\s*[^0\s;]',  # border: 1px solid #000
            r'border-width\s*:\s*[^0\s;]',  # border-width: 1px
            r'border-style\s*:\s*(?!none)[^;\s]',  # border-style: solid
            r'border-color\s*:\s*[^;\s]',  # border-color: #000
            r'border-top\s*:\s*[^0\s;]',  # border-top: 1px solid
            r'border-bottom\s*:\s*[^0\s;]',  # border-bottom: 1px solid
            r'border-left\s*:\s*[^0\s;]',  # border-left: 1px solid
            r'border-right\s*:\s*[^0\s;]'  # border-right: 1px solid
        ]
        
        for pattern in border_patterns:
            if re.search(pattern, style_lower):
                return True
        
        return False
    
    def get_categories(self) -> List[str]:
        """获取所有可能的分类"""
        return self.categories


class TableBorderCrawlerSystem:
    """表格边框分类爬虫系统主类"""
    
    def __init__(self, config: CrawlConfig = None):
        self.config = config or CrawlConfig()
        self.crawler = WebCrawler(self.config)
        self.classifier = TableBorderClassifier()
        self.link_extractor = LinkExtractor()
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('TableBorderCrawlerSystem')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def extract_and_classify_links(self, source_url: str, output_file: str) -> None:
        """从源URL提取链接并进行表格边框分类"""
        self.logger.info(f"开始处理源URL: {source_url}")
        
        # 1. 爬取源页面
        source_result = self.crawler.crawl_url(source_url)
        if not source_result.success:
            self.logger.error(f"无法爬取源页面: {source_result.error_message}")
            return
        
        # 2. 提取链接
        links = self.link_extractor.extract_links_from_html(source_result.html, source_url)
        self.logger.info(f"从源页面提取到 {len(links)} 个链接")
        
        if not links:
            self.logger.warning("未找到任何有效链接")
            return
        
        # 3. 爬取和分类每个链接
        results = []
        for i, link in enumerate(links, 1):
            self.logger.info(f"处理第 {i}/{len(links)} 个链接: {link['text']}")
            
            # 爬取链接
            crawl_result = self.crawler.crawl_url(link['url'])
            
            # 分类
            if crawl_result.success:
                classification = self.classifier.classify(crawl_result)
                self.logger.info(f"分类结果: {classification}")
            else:
                classification = "爬取失败"
                self.logger.error(f"爬取失败: {crawl_result.error_message}")
            
            results.append({
                '单位名称': link['text'],
                '子链接': link['url'],
                '分类': classification
            })
            
            # 添加延迟避免过于频繁的请求
            time.sleep(self.config.rate_limit_delay)
        
        # 4. 保存结果
        self._save_results(results, output_file)
        self._print_summary(results)
    
    def _save_results(self, results: List[Dict], output_file: str) -> None:
        """保存结果到CSV文件"""
        try:
            df = pd.DataFrame(results)
            df.to_csv(output_file, index=False, encoding='utf-8')
            self.logger.info(f"结果已保存到: {output_file}")
        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
    
    def _print_summary(self, results: List[Dict]) -> None:
        """打印处理摘要"""
        total = len(results)
        categories = {}
        
        for result in results:
            category = result['分类']
            categories[category] = categories.get(category, 0) + 1
        
        self.logger.info(f"\n📊 表格边框分类摘要:")
        self.logger.info(f"总记录数: {total}")
        for category, count in categories.items():
            percentage = (count / total) * 100 if total > 0 else 0
            self.logger.info(f"  {category}: {count} 个 ({percentage:.1f}%)")


def main():
    """主函数"""
    print("🚀 表格边框样式分类爬虫系统")
    print("=" * 50)
    print("根据HTML表格是否有边框线进行分类：")
    print("  - 第一类：无表格线（无边框或边框为0）")
    print("  - 第二类：有表格线（有明显的边框设置）")
    print()
    
    # 配置系统
    config = CrawlConfig()
    system = TableBorderCrawlerSystem(config)
    
    # 获取用户输入
    source_url = input("请输入源URL (默认: http://***********/phone/20201227.html): ").strip()
    if not source_url:
        source_url = "http://***********/phone/20201227.html"
    
    output_file = input("请输入输出CSV文件名 (默认: final_units_data.csv): ").strip()
    if not output_file:
        output_file = "final_units_data.csv"
    
    # 开始处理
    system.extract_and_classify_links(source_url, output_file)


if __name__ == "__main__":
    main()
