#!/usr/bin/env python3
"""
测试通用爬虫系统
"""
from universal_web_crawler import (
    CrawlConfig, 
    UniversalWebCrawlerSystem, 
    ContentBasedClassifier,
    TableStyleClassifier
)

def test_source_url_extraction():
    """测试从源URL提取链接"""
    print("🧪 测试从源URL提取链接...")
    
    config = CrawlConfig()
    classifier = ContentBasedClassifier()
    system = UniversalWebCrawlerSystem(config, classifier)
    
    source_url = "http://172.18.1.16/phone/20201227.html"
    output_file = "extracted_links_test.csv"
    
    try:
        system.extract_and_classify_links(source_url, output_file)
        print("✅ 源URL链接提取测试完成")
    except Exception as e:
        print(f"❌ 源URL链接提取测试失败: {e}")

def test_csv_processing():
    """测试CSV文件处理"""
    print("\n🧪 测试CSV文件处理...")
    
    config = CrawlConfig()
    classifier = TableStyleClassifier()
    system = UniversalWebCrawlerSystem(config, classifier)
    
    input_file = "final_units_data1.csv"
    output_file = "final_units_data_processed.csv"
    
    try:
        system.process_existing_csv(input_file, output_file)
        print("✅ CSV文件处理测试完成")
    except Exception as e:
        print(f"❌ CSV文件处理测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试通用爬虫系统")
    print("=" * 50)
    
    # 选择测试模式
    print("选择测试模式:")
    print("1. 测试从源URL提取链接")
    print("2. 测试CSV文件处理")
    print("3. 运行所有测试")
    
    choice = input("请输入选择 (1, 2, 或 3，默认为 2): ").strip()
    
    if choice == "1":
        test_source_url_extraction()
    elif choice == "3":
        test_source_url_extraction()
        test_csv_processing()
    else:
        test_csv_processing()

if __name__ == "__main__":
    main()
