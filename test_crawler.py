#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试爬虫程序
用于验证各个模块的功能
"""

import asyncio
import sys
from base_crawler import BaseCrawler, CrawlConfig
from phone_extractor import PhoneExtractor
from csv_processor import CSVProcessor


async def test_main_page():
    """测试主页面爬取"""
    print("🧪 测试1: 主页面爬取")
    print("-" * 40)
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    
    # 爬取主页面
    main_url = "http://172.18.1.16/phone/20201227.html"
    html_content = await crawler.crawl_url(main_url)
    
    if html_content:
        print(f"✅ 主页面爬取成功，内容长度: {len(html_content)}")
        
        # 提取电话信息
        records = extractor.extract_phone_info(html_content, "矿领导", "auto")
        print(f"✅ 提取到 {len(records)} 条电话记录:")
        
        for i, record in enumerate(records, 1):
            print(f"  {i}. {record.phone} - {record.name} ({record.unit})")
        
        return True
    else:
        print("❌ 主页面爬取失败")
        return False


async def test_single_unit():
    """测试单个单位爬取"""
    print("\n🧪 测试2: 单个单位爬取")
    print("-" * 40)
    
    # 加载CSV数据
    csv_processor = CSVProcessor()
    if not csv_processor.load_data():
        print("❌ CSV数据加载失败")
        return False
    
    # 获取行政工作部记录
    record = csv_processor.get_unit_record("行政工作部")
    if not record:
        print("❌ 未找到行政工作部记录")
        return False
    
    print(f"📋 单位: {record.unit_name}")
    print(f"🔗 链接: {record.sub_link}")
    print(f"📊 分类: {record.classification}")
    
    # 爬取单位页面
    crawler = BaseCrawler()
    html_content = await crawler.crawl_url(record.sub_link)
    
    if html_content:
        print(f"✅ 单位页面爬取成功，内容长度: {len(html_content)}")
        
        # 提取电话信息
        extractor = PhoneExtractor()
        phone_records = extractor.extract_phone_info(
            html_content, 
            record.unit_name, 
            record.classification
        )
        
        print(f"✅ 提取到 {len(phone_records)} 条电话记录:")
        for i, phone_record in enumerate(phone_records, 1):
            print(f"  {i}. {phone_record.phone} - {phone_record.name}")
        
        return True
    else:
        print("❌ 单位页面爬取失败")
        return False


async def test_csv_processing():
    """测试CSV处理"""
    print("\n🧪 测试3: CSV数据处理")
    print("-" * 40)
    
    csv_processor = CSVProcessor()
    if not csv_processor.load_data():
        print("❌ CSV数据加载失败")
        return False
    
    # 显示统计信息
    summary = csv_processor.get_summary()
    print(f"📊 总行数: {summary['total_rows']}")
    print(f"📋 分类统计: {summary['classification_counts']}")
    
    # 测试迭代前3条记录
    print("\n📋 前3条记录:")
    count = 0
    for record in csv_processor.iter_all_records():
        print(f"  {count + 1}. {record.unit_name} ({record.classification})")
        count += 1
        if count >= 3:
            break
    
    return True


async def test_batch_processing():
    """测试批量处理（前5个单位）"""
    print("\n🧪 测试4: 批量处理（前5个单位）")
    print("-" * 40)
    
    # 加载CSV数据
    csv_processor = CSVProcessor()
    if not csv_processor.load_data():
        print("❌ CSV数据加载失败")
        return False
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    
    all_records = []
    processed_count = 0
    
    # 处理前5个单位
    for record in csv_processor.iter_all_records():
        if processed_count >= 5:
            break
        
        processed_count += 1
        print(f"\n📋 处理第 {processed_count} 个单位: {record.unit_name}")
        
        try:
            # 爬取页面
            html_content = await crawler.crawl_url(record.sub_link)
            if not html_content:
                print(f"❌ 爬取失败: {record.unit_name}")
                continue
            
            # 提取电话信息
            phone_records = extractor.extract_phone_info(
                html_content,
                record.unit_name,
                record.classification
            )
            
            if phone_records:
                all_records.extend(phone_records)
                print(f"✅ 提取到 {len(phone_records)} 条电话记录")
                for phone_record in phone_records:
                    print(f"  📞 {phone_record.phone} - {phone_record.name}")
            else:
                print(f"⚠️ 未提取到电话信息")
            
            # 添加延迟
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"❌ 处理异常: {e}")
    
    print(f"\n🎉 批量处理完成，总共提取到 {len(all_records)} 条电话记录")
    return True


async def main():
    """主测试函数"""
    print("🧪 模块化爬虫系统测试")
    print("=" * 50)
    
    tests = [
        ("主页面爬取", test_main_page),
        ("单个单位爬取", test_single_unit),
        ("CSV数据处理", test_csv_processing),
        ("批量处理", test_batch_processing)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n🎯 测试结果汇总:")
    print("=" * 50)
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n📊 总体结果: {passed}/{total} 个测试通过")


if __name__ == "__main__":
    asyncio.run(main())
