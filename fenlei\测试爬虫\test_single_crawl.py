#!/usr/bin/env python3
"""
测试单个URL爬取
"""
import requests
import json
import time

def test_single_url():
    api_base_url = "http://172.18.151.239:11235"
    api_token = "123456"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_token}"
    }
    
    # 测试URL
    test_url = "http://172.18.1.16/phone/机关部室/xzgzb.html"
    
    crawl_endpoint = f"{api_base_url}/crawl"
    
    payload = {
        "urls": [test_url]
    }
    
    try:
        print(f"正在测试爬取: {test_url}")
        
        # 发起爬取请求
        response = requests.post(crawl_endpoint, headers=headers, json=payload, timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            print(f"获取到task_id: {task_id}")
            
            # 尝试获取任务结果
            if task_id:
                get_result(api_base_url, headers, task_id)
        
    except Exception as e:
        print(f"错误: {e}")

def get_result(api_base_url, headers, task_id):
    result_endpoint = f"{api_base_url}/task/{task_id}"
    
    for i in range(10):
        try:
            time.sleep(3)
            print(f"尝试获取结果 {i+1}/10...")
            response = requests.get(result_endpoint, headers=headers, timeout=30)
            
            print(f"结果状态码: {response.status_code}")
            print(f"结果内容: {response.text[:500]}...")
            
            if response.status_code == 200:
                result = response.json()
                status = result.get("status", "")
                print(f"任务状态: {status}")
                
                if status == "completed":
                    print("任务完成!")
                    break
                elif status == "failed":
                    print("任务失败!")
                    break
            
        except Exception as e:
            print(f"获取结果时出错: {e}")

if __name__ == "__main__":
    test_single_url()
