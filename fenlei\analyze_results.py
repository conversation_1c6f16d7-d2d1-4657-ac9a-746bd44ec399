#!/usr/bin/env python3
"""
分析分类结果
"""
import pandas as pd

def analyze_classification_results():
    """
    分析分类结果
    """
    # 读取分类结果
    df = pd.read_csv('final_units_data_classified.csv', encoding='utf-8')

    print("🔍 分类结果分析")
    print("=" * 50)

    # 检查列名
    print(f"列名: {list(df.columns)}")

    # 总体统计
    total_count = len(df)
    print(f"总记录数: {total_count}")

    # 分类统计
    if '分类' in df.columns:
        classification_counts = df['分类'].value_counts()
    else:
        print("❌ 未找到'分类'列")
        return
    print(f"\n📊 分类统计:")
    for category, count in classification_counts.items():
        percentage = (count / total_count) * 100
        print(f"  {category}: {count} 个 ({percentage:.1f}%)")
    
    # 按URL路径分析
    print(f"\n📁 按URL路径分析:")
    
    # 机关部室
    jiguan_df = df[df['子链接'].str.contains('机关部室')]
    print(f"  机关部室: {len(jiguan_df)} 个")
    jiguan_stats = jiguan_df['分类'].value_counts()
    for category, count in jiguan_stats.items():
        print(f"    - {category}: {count} 个")
    
    # 主要生产单位
    shengchan_df = df[df['子链接'].str.contains('主要生产单位')]
    print(f"  主要生产单位: {len(shengchan_df)} 个")
    shengchan_stats = shengchan_df['分类'].value_counts()
    for category, count in shengchan_stats.items():
        print(f"    - {category}: {count} 个")
    
    # 主要经营企业
    jingying_df = df[df['子链接'].str.contains('主要经营企业')]
    print(f"  主要经营企业: {len(jingying_df)} 个")
    jingying_stats = jingying_df['分类'].value_counts()
    for category, count in jingying_stats.items():
        print(f"    - {category}: {count} 个")
    
    # 辅助生产企业
    fuzhu_df = df[df['子链接'].str.contains('辅助生产企业')]
    print(f"  辅助生产企业: {len(fuzhu_df)} 个")
    fuzhu_stats = fuzhu_df['分类'].value_counts()
    for category, count in fuzhu_stats.items():
        print(f"    - {category}: {count} 个")
    
    # 主要服务单位
    fuwu_df = df[df['子链接'].str.contains('主要服务单位')]
    print(f"  主要服务单位: {len(fuwu_df)} 个")
    fuwu_stats = fuwu_df['分类'].value_counts()
    for category, count in fuwu_stats.items():
        print(f"    - {category}: {count} 个")
    
    # 其他
    other_df = df[~df['子链接'].str.contains('机关部室|主要生产单位|主要经营企业|辅助生产企业|主要服务单位')]
    if len(other_df) > 0:
        print(f"  其他: {len(other_df)} 个")
        other_stats = other_df['分类'].value_counts()
        for category, count in other_stats.items():
            print(f"    - {category}: {count} 个")
    
    print(f"\n✅ 分析完成！")
    
    # 显示一些具体例子
    print(f"\n📋 分类示例:")
    print(f"管理类示例:")
    management_examples = df[df['分类'] == '管理类']['单位名称'].head(5).tolist()
    for example in management_examples:
        print(f"  - {example}")
    
    print(f"\n生产类示例:")
    production_examples = df[df['分类'] == '生产类']['单位名称'].head(5).tolist()
    for example in production_examples:
        print(f"  - {example}")

if __name__ == "__main__":
    analyze_classification_results()
