#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比我的程序与fully_universal_crawler.py的结果
确保数据完全一致
"""

import asyncio
import csv
import pandas as pd
from datetime import datetime
from base_crawler import BaseCrawler
from phone_extractor import PhoneExtractor


async def test_my_crawler():
    """测试我的爬虫程序，只爬取采矿场"""
    print("🎯 测试我的爬虫程序 - 采矿场")
    print("=" * 60)
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    
    test_url = "http://172.18.1.16/phone/主要生产单位/采矿场/ckc.html"
    print(f"🌐 测试URL: {test_url}")
    
    html_content = await crawler.crawl_url(test_url)
    if not html_content:
        print("❌ 页面爬取失败")
        return []
    
    print(f"✅ 页面爬取成功，内容长度: {len(html_content)}")
    
    # 使用我的提取器
    records = extractor.extract_phone_info(html_content, "采矿场", "有表格线")
    
    print(f"✅ 我的程序提取到 {len(records)} 条电话记录")
    
    # 转换为标准格式
    my_data = []
    for record in records:
        my_data.append({
            '电话号码': record.phone,
            '电话用户名': record.name,
            '单位名称': record.unit
        })
    
    return my_data


def load_standard_data():
    """加载fully_universal_crawler.py的标准数据"""
    print("\n🎯 加载fully_universal_crawler.py的标准数据")
    print("=" * 60)
    
    # 查找最新的标准数据文件
    import glob
    import os
    
    pattern = "采矿场_电话信息_完全通用_*.csv"
    files = glob.glob(pattern)
    
    if not files:
        print("❌ 未找到标准数据文件")
        return []
    
    # 选择最新的文件
    latest_file = max(files, key=os.path.getctime)
    print(f"📁 加载标准数据文件: {latest_file}")
    
    try:
        df = pd.read_csv(latest_file, encoding='utf-8')
        print(f"✅ 标准数据加载成功，共 {len(df)} 条记录")
        
        # 转换为标准格式
        standard_data = []
        for _, row in df.iterrows():
            standard_data.append({
                '电话号码': str(row['电话号码']),
                '电话用户名': str(row['电话用户名']),
                '单位名称': str(row['单位名称'])
            })
        
        return standard_data
    except Exception as e:
        print(f"❌ 加载标准数据失败: {e}")
        return []


def compare_data(my_data, standard_data):
    """详细对比两个数据集"""
    print(f"\n🎯 详细数据对比")
    print("=" * 60)
    
    print(f"📊 数据量对比:")
    print(f"  我的程序: {len(my_data)} 条")
    print(f"  标准程序: {len(standard_data)} 条")
    print(f"  差异: {len(my_data) - len(standard_data)} 条")
    
    if len(my_data) != len(standard_data):
        print("❌ 数据量不一致！")
    else:
        print("✅ 数据量一致")
    
    # 创建标准数据的查找字典
    standard_dict = {}
    for item in standard_data:
        key = f"{item['电话号码']}|{item['单位名称']}"
        standard_dict[key] = item['电话用户名']
    
    # 创建我的数据的查找字典
    my_dict = {}
    for item in my_data:
        key = f"{item['电话号码']}|{item['单位名称']}"
        my_dict[key] = item['电话用户名']
    
    # 找出差异
    print(f"\n📋 详细差异分析:")
    
    # 我有但标准没有的
    my_only = []
    for key, name in my_dict.items():
        if key not in standard_dict:
            phone, unit = key.split('|', 1)
            my_only.append({'电话号码': phone, '单位名称': unit, '电话用户名': name})
    
    # 标准有但我没有的
    standard_only = []
    for key, name in standard_dict.items():
        if key not in my_dict:
            phone, unit = key.split('|', 1)
            standard_only.append({'电话号码': phone, '单位名称': unit, '电话用户名': name})
    
    # 都有但用户名不同的
    name_diff = []
    for key in my_dict:
        if key in standard_dict:
            if my_dict[key] != standard_dict[key]:
                phone, unit = key.split('|', 1)
                name_diff.append({
                    '电话号码': phone,
                    '单位名称': unit,
                    '我的用户名': my_dict[key],
                    '标准用户名': standard_dict[key]
                })
    
    print(f"  我有但标准没有: {len(my_only)} 条")
    print(f"  标准有但我没有: {len(standard_only)} 条")
    print(f"  用户名不同: {len(name_diff)} 条")
    
    # 显示详细差异
    if my_only:
        print(f"\n❌ 我有但标准没有的记录 ({len(my_only)} 条):")
        for i, item in enumerate(my_only[:10], 1):  # 只显示前10条
            print(f"  {i}. {item['电话号码']} - {item['电话用户名']} ({item['单位名称']})")
        if len(my_only) > 10:
            print(f"  ... 还有 {len(my_only) - 10} 条")
    
    if standard_only:
        print(f"\n❌ 标准有但我没有的记录 ({len(standard_only)} 条):")
        for i, item in enumerate(standard_only[:10], 1):  # 只显示前10条
            print(f"  {i}. {item['电话号码']} - {item['电话用户名']} ({item['单位名称']})")
        if len(standard_only) > 10:
            print(f"  ... 还有 {len(standard_only) - 10} 条")
    
    if name_diff:
        print(f"\n⚠️ 用户名不同的记录 ({len(name_diff)} 条):")
        for i, item in enumerate(name_diff[:10], 1):  # 只显示前10条
            print(f"  {i}. {item['电话号码']} ({item['单位名称']})")
            print(f"     我的: {item['我的用户名']}")
            print(f"     标准: {item['标准用户名']}")
        if len(name_diff) > 10:
            print(f"  ... 还有 {len(name_diff) - 10} 条")
    
    # 计算匹配率
    total_standard = len(standard_data)
    exact_matches = 0
    
    for item in standard_data:
        key = f"{item['电话号码']}|{item['单位名称']}"
        if key in my_dict and my_dict[key] == item['电话用户名']:
            exact_matches += 1
    
    match_rate = (exact_matches / total_standard * 100) if total_standard > 0 else 0
    
    print(f"\n📈 匹配统计:")
    print(f"  完全匹配: {exact_matches}/{total_standard} 条")
    print(f"  匹配率: {match_rate:.1f}%")
    
    return {
        'my_only': my_only,
        'standard_only': standard_only,
        'name_diff': name_diff,
        'match_rate': match_rate,
        'exact_matches': exact_matches,
        'total_standard': total_standard
    }


def save_comparison_results(my_data, standard_data, comparison):
    """保存对比结果"""
    print(f"\n🎯 保存对比结果")
    print("=" * 60)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存我的数据
    my_filename = f"我的程序结果_{timestamp}.csv"
    try:
        with open(my_filename, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['电话号码', '单位名称', '电话用户名']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(my_data)
        print(f"✅ 我的程序结果保存: {my_filename}")
    except Exception as e:
        print(f"❌ 保存我的程序结果失败: {e}")
    
    # 保存对比报告
    report_filename = f"数据对比报告_{timestamp}.txt"
    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("数据对比报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"数据量对比:\n")
            f.write(f"  我的程序: {len(my_data)} 条\n")
            f.write(f"  标准程序: {len(standard_data)} 条\n")
            f.write(f"  差异: {len(my_data) - len(standard_data)} 条\n\n")
            
            f.write(f"匹配统计:\n")
            f.write(f"  完全匹配: {comparison['exact_matches']}/{comparison['total_standard']} 条\n")
            f.write(f"  匹配率: {comparison['match_rate']:.1f}%\n\n")
            
            f.write(f"差异统计:\n")
            f.write(f"  我有但标准没有: {len(comparison['my_only'])} 条\n")
            f.write(f"  标准有但我没有: {len(comparison['standard_only'])} 条\n")
            f.write(f"  用户名不同: {len(comparison['name_diff'])} 条\n\n")
            
            if comparison['my_only']:
                f.write("我有但标准没有的记录:\n")
                for item in comparison['my_only']:
                    f.write(f"  {item['电话号码']} - {item['电话用户名']} ({item['单位名称']})\n")
                f.write("\n")
            
            if comparison['standard_only']:
                f.write("标准有但我没有的记录:\n")
                for item in comparison['standard_only']:
                    f.write(f"  {item['电话号码']} - {item['电话用户名']} ({item['单位名称']})\n")
                f.write("\n")
            
            if comparison['name_diff']:
                f.write("用户名不同的记录:\n")
                for item in comparison['name_diff']:
                    f.write(f"  {item['电话号码']} ({item['单位名称']})\n")
                    f.write(f"    我的: {item['我的用户名']}\n")
                    f.write(f"    标准: {item['标准用户名']}\n")
                f.write("\n")
        
        print(f"✅ 对比报告保存: {report_filename}")
    except Exception as e:
        print(f"❌ 保存对比报告失败: {e}")


async def main():
    """主函数"""
    print("🕷️ 数据对比程序")
    print("对比我的程序与fully_universal_crawler.py的结果")
    print("=" * 70)
    
    try:
        # 1. 运行我的程序
        my_data = await test_my_crawler()
        
        # 2. 加载标准数据
        standard_data = load_standard_data()
        
        if not my_data or not standard_data:
            print("❌ 数据加载失败，无法进行对比")
            return
        
        # 3. 详细对比
        comparison = compare_data(my_data, standard_data)
        
        # 4. 保存结果
        save_comparison_results(my_data, standard_data, comparison)
        
        # 5. 总结
        print(f"\n🎉 数据对比完成！")
        print("=" * 70)
        print("📊 对比结果总结:")
        print(f"  数据量: 我的 {len(my_data)} vs 标准 {len(standard_data)}")
        print(f"  匹配率: {comparison['match_rate']:.1f}%")
        
        if comparison['match_rate'] == 100.0 and len(my_data) == len(standard_data):
            print("🎉 完美匹配！数据完全一致！")
        else:
            print("⚠️ 存在差异，需要进一步修复")
            print("📋 主要问题:")
            if len(my_data) != len(standard_data):
                print(f"  - 数据量不一致")
            if comparison['my_only']:
                print(f"  - 多提取了 {len(comparison['my_only'])} 条记录")
            if comparison['standard_only']:
                print(f"  - 漏提取了 {len(comparison['standard_only'])} 条记录")
            if comparison['name_diff']:
                print(f"  - {len(comparison['name_diff'])} 条记录的用户名不同")
        
    except Exception as e:
        print(f"❌ 对比过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
