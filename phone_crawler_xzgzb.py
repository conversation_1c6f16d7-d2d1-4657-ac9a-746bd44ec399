#!/usr/bin/env python3
"""
行政工作部电话信息爬虫
使用crawl4ai爬取指定单位的电话信息
"""

import asyncio
import csv
import json
import re
import pandas as pd
import requests
import time
from typing import List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phone_crawler_xzgzb.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PhoneCrawlerXZGZB:
    def __init__(self):
        self.crawl4ai_url = "http://172.18.151.239:11235"
        self.csv_file = "final_units_data.csv"
        self.target_unit = "行政工作部"
        self.phone_data = []
        
    def read_csv_data(self) -> Optional[str]:
        """读取CSV文件，获取行政工作部的子链接"""
        try:
            df = pd.read_csv(self.csv_file, encoding='utf-8')
            logger.info(f"成功读取CSV文件: {self.csv_file}")
            logger.info(f"CSV文件包含 {len(df)} 行数据")
            
            # 查找行政工作部的记录
            target_row = df[df['单位名称'] == self.target_unit]
            
            if target_row.empty:
                logger.error(f"未找到单位: {self.target_unit}")
                return None
            
            sub_link = target_row.iloc[0]['子链接']
            logger.info(f"找到目标单位: {self.target_unit}")
            logger.info(f"子链接: {sub_link}")
            
            return sub_link
            
        except Exception as e:
            logger.error(f"读取CSV文件失败: {e}")
            return None
    
    async def crawl_with_crawl4ai(self, url: str) -> Optional[str]:
        """使用crawl4ai爬取网页内容"""
        try:
            logger.info(f"开始使用crawl4ai爬取: {url}")

            # 测试crawl4ai服务连接
            try:
                test_response = requests.get(f"{self.crawl4ai_url}/health", timeout=5)
                if test_response.status_code == 200:
                    logger.info("✅ crawl4ai服务连接成功")
                else:
                    logger.warning(f"crawl4ai服务响应异常: {test_response.status_code}")
            except Exception as e:
                logger.error(f"❌ crawl4ai服务连接失败: {e}")
                return None

            # 使用异步任务方式（参考成功的程序）
            logger.info("尝试异步任务方式...")

            # 1. 提交异步任务
            submit_url = f"{self.crawl4ai_url}/crawl"
            payload = {
                'urls': [url],
                'bypass_cache': True,
                'include_raw_html': True
            }

            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer 123456'
            }

            submit_response = requests.post(submit_url, json=payload, headers=headers, timeout=30)

            if submit_response.status_code != 200:
                logger.error(f"❌ 提交异步任务失败: {submit_response.status_code}")
                return None

            submit_result = submit_response.json()
            logger.info(f"API响应键: {list(submit_result.keys())}")

            # 处理不同的响应格式
            if 'task_id' in submit_result:
                # 异步任务模式
                task_id = submit_result.get('task_id')
                logger.info(f"✅ 异步任务提交成功，任务ID: {task_id}")
            elif submit_result.get('results'):
                # 同步返回结果
                logger.info("✅ 同步获取结果")
                first_result = submit_result['results'][0]
                html_content = (first_result.get('cleaned_html') or
                              first_result.get('raw_html') or
                              first_result.get('html', ''))
                if html_content:
                    logger.info(f"✅ 获取HTML内容成功，长度: {len(html_content)} 字符")
                    return html_content
                else:
                    logger.error("❌ 同步结果无HTML内容")
                    return None
            else:
                logger.error(f"❌ 未知响应格式: {submit_result}")
                return None

            # 2. 轮询任务状态
            status_url = f"{self.crawl4ai_url}/task/{task_id}"
            max_attempts = 30

            for attempt in range(max_attempts):
                logger.info(f"检查任务状态 ({attempt + 1}/{max_attempts})...")

                headers = {'Authorization': 'Bearer 123456'}
                status_response = requests.get(status_url, headers=headers, timeout=10)
                if status_response.status_code != 200:
                    logger.warning(f"状态查询失败: {status_response.status_code}")
                    await asyncio.sleep(2)
                    continue

                status_result = status_response.json()
                task_status = status_result.get('status', 'unknown')

                logger.info(f"任务状态: {task_status}")

                if task_status == 'completed':
                    logger.info("✅ 任务完成，获取结果...")

                    # 获取HTML内容
                    html_content = None
                    if status_result.get('result') and status_result['result'].get('results'):
                        first_result = status_result['result']['results'][0]
                        html_content = (first_result.get('cleaned_html') or
                                      first_result.get('raw_html') or
                                      first_result.get('html', ''))
                    elif status_result.get('results'):
                        first_result = status_result['results'][0]
                        html_content = (first_result.get('cleaned_html') or
                                      first_result.get('raw_html') or
                                      first_result.get('html', ''))

                    if html_content:
                        logger.info(f"✅ 获取HTML内容成功，长度: {len(html_content)} 字符")
                        return html_content
                    else:
                        logger.error("❌ 任务完成但无法找到HTML内容")
                        return None

                elif task_status == 'failed':
                    logger.error("❌ 任务执行失败")
                    return None

                # 等待后继续检查
                await asyncio.sleep(2)

            logger.error("❌ 任务超时")
            return None

        except Exception as e:
            logger.error(f"crawl4ai爬取失败: {e}")
            return None
    
    def extract_phone_info(self, html_content: str, unit_name: str) -> List[Dict]:
        """从HTML内容中提取电话信息"""
        logger.info("开始提取电话信息...")

        phone_records = []

        # 电话号码正则表达式 (7位或11位数字)
        phone_pattern = r'\b(?:\d{7}|\d{11})\b'

        # 分行处理HTML内容
        lines = html_content.split('\n')

        for line_num, line in enumerate(lines, 1):
            # 清理HTML标签
            clean_line = re.sub(r'<[^>]+>', '', line).strip()

            if not clean_line:
                continue

            # 查找电话号码
            phones = re.findall(phone_pattern, clean_line)

            for phone in phones:
                # 在电话号码前查找中文名称
                phone_index = clean_line.find(phone)
                if phone_index > 0:
                    # 获取电话号码前的文本
                    before_phone = clean_line[:phone_index].strip()

                    # 提取完整的中文名称（改进算法）
                    name = self.extract_complete_name(before_phone)

                    if name and len(name) >= 2:  # 确保名称至少2个字符
                        phone_record = {
                            '电话号码': phone,
                            '单位名称': unit_name,
                            '电话用户名': name
                        }

                        # 避免重复记录
                        if phone_record not in phone_records:
                            phone_records.append(phone_record)
                            logger.info(f"✅ 提取到: {phone} - {name}")

        logger.info(f"共提取到 {len(phone_records)} 条电话记录")
        return phone_records

    def extract_complete_name(self, text: str) -> str:
        """提取完整的中文名称"""
        if not text:
            return ""

        # 移除常见的分隔符和标点
        text = re.sub(r'[：:、，,\(\)（）\[\]【】\s]+', ' ', text)

        # 分割成词组
        words = text.split()

        # 从右到左查找最后一个包含中文的词组
        for word in reversed(words):
            # 提取连续的中文字符
            chinese_chars = re.findall(r'[\u4e00-\u9fa5]+', word)
            if chinese_chars:
                # 取最长的中文字符串
                longest_chinese = max(chinese_chars, key=len)
                if len(longest_chinese) >= 2:
                    return longest_chinese

        # 如果上面的方法没找到，尝试从整个文本中提取最后的连续中文
        all_chinese = re.findall(r'[\u4e00-\u9fa5]+', text)
        if all_chinese:
            # 返回最后一个中文字符串
            return all_chinese[-1]

        return ""
    
    def save_results(self, phone_data: List[Dict]):
        """保存结果到CSV文件"""
        if not phone_data:
            logger.warning("没有数据可保存")
            return
        
        # 保存CSV文件
        csv_filename = f"{self.target_unit}_电话信息.csv"
        with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['电话号码', '单位名称', '电话用户名']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(phone_data)
        
        # 保存JSON文件
        json_filename = f"{self.target_unit}_电话信息.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(phone_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 数据已保存:")
        logger.info(f"   - {csv_filename}")
        logger.info(f"   - {json_filename}")
        
        # 显示结果统计
        self.display_results(phone_data)
    
    def display_results(self, phone_data: List[Dict]):
        """显示提取结果"""
        print(f"\n{'='*60}")
        print(f"🏢 {self.target_unit} 电话信息提取结果")
        print(f"{'='*60}")
        print(f"📊 共提取到 {len(phone_data)} 条电话记录:")
        print()
        
        for i, record in enumerate(phone_data, 1):
            print(f"{i:2d}. {record['电话号码']} - {record['电话用户名']}")
        
        print(f"\n{'='*60}")
        print(f"💾 数据已保存到:")
        print(f"   - {self.target_unit}_电话信息.csv")
        print(f"   - {self.target_unit}_电话信息.json")
        print(f"📝 详细日志: phone_crawler_xzgzb.log")
    
    async def run(self):
        """主运行函数"""
        print(f"🚀 {self.target_unit} 电话信息爬虫")
        print(f"{'='*50}")
        print(f"📁 CSV文件: {self.csv_file}")
        print(f"🎯 目标单位: {self.target_unit}")
        print(f"🌐 crawl4ai服务: {self.crawl4ai_url}")
        print(f"{'='*50}")
        
        # 1. 读取CSV文件获取子链接
        sub_link = self.read_csv_data()
        if not sub_link:
            print("❌ 无法获取子链接，程序退出")
            return
        
        # 2. 使用crawl4ai爬取网页
        html_content = await self.crawl_with_crawl4ai(sub_link)
        if not html_content:
            print("❌ 无法获取网页内容，程序退出")
            return
        
        # 3. 提取电话信息
        phone_data = self.extract_phone_info(html_content, self.target_unit)
        if not phone_data:
            print("❌ 未提取到任何电话信息")
            return
        
        # 4. 保存结果
        self.save_results(phone_data)
        self.phone_data = phone_data

async def main():
    """主函数"""
    crawler = PhoneCrawlerXZGZB()
    await crawler.run()

if __name__ == "__main__":
    asyncio.run(main())
