#!/usr/bin/env python3
"""
精确的表格样式分类器
基于实际的HTML表格结构特征进行分类
"""

import requests
import time
import json
import re
import logging
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
from bs4 import BeautifulSoup
import pandas as pd


@dataclass
class CrawlConfig:
    """爬虫配置类"""
    api_base_url: str = "http://172.18.151.239:11235"
    api_token: str = "123456"
    max_retries: int = 15
    retry_delay: float = 3.0
    request_timeout: int = 30
    rate_limit_delay: float = 1.0


@dataclass
class CrawlResult:
    """爬取结果数据类"""
    url: str
    success: bool
    html: str = ""
    markdown: str = ""
    cleaned_html: str = ""
    error_message: str = ""
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class LinkExtractor:
    """链接提取器"""
    
    @staticmethod
    def extract_links_from_html(html_content: str, base_url: str) -> List[Dict[str, str]]:
        """从HTML中提取链接"""
        soup = BeautifulSoup(html_content, 'html.parser')
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text(strip=True)
            
            # 处理相对链接
            absolute_url = urljoin(base_url, href)
            
            # 过滤掉无效链接
            if LinkExtractor._is_valid_link(absolute_url, text):
                links.append({
                    'text': text,
                    'url': absolute_url,
                    'original_href': href
                })
        
        return links
    
    @staticmethod
    def _is_valid_link(url: str, text: str) -> bool:
        """验证链接是否有效"""
        if not text or len(text.strip()) < 2:
            return False
        
        if url.startswith(('javascript:', 'mailto:', '#')):
            return False
        
        parsed = urlparse(url)
        if not parsed.scheme in ['http', 'https']:
            return False
        
        return True


class WebCrawler:
    """网页爬虫核心类"""
    
    def __init__(self, config: CrawlConfig):
        self.config = config
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.api_token}"
        }
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('WebCrawler')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def crawl_url(self, url: str) -> CrawlResult:
        """爬取单个URL"""
        self.logger.info(f"开始爬取: {url}")
        
        try:
            task_id = self._submit_crawl_task(url)
            if not task_id:
                return CrawlResult(url=url, success=False, error_message="Failed to submit crawl task")
            
            return self._get_crawl_result(task_id, url)
            
        except Exception as e:
            self.logger.error(f"爬取URL时发生错误 {url}: {e}")
            return CrawlResult(url=url, success=False, error_message=str(e))
    
    def _submit_crawl_task(self, url: str) -> Optional[str]:
        """提交爬取任务"""
        crawl_endpoint = f"{self.config.api_base_url}/crawl"
        payload = {"urls": [url]}
        
        try:
            response = requests.post(
                crawl_endpoint, 
                headers=self.headers, 
                json=payload, 
                timeout=self.config.request_timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("task_id")
            else:
                self.logger.error(f"提交任务失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"网络请求错误: {e}")
            return None
    
    def _get_crawl_result(self, task_id: str, url: str) -> CrawlResult:
        """获取爬取结果"""
        result_endpoint = f"{self.config.api_base_url}/task/{task_id}"
        
        for attempt in range(self.config.max_retries):
            try:
                time.sleep(self.config.retry_delay)
                response = requests.get(
                    result_endpoint, 
                    headers=self.headers, 
                    timeout=self.config.request_timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    status = result.get("status", "")
                    
                    if status == "completed":
                        self.logger.info(f"爬取成功: {url}")
                        return self._parse_completed_result(result, url)
                    elif status == "failed":
                        error_msg = result.get("error", "Unknown error")
                        self.logger.error(f"任务失败: {error_msg}")
                        return CrawlResult(url=url, success=False, error_message=error_msg)
                    else:
                        self.logger.info(f"任务进行中... 状态: {status} (尝试 {attempt + 1}/{self.config.max_retries})")
                        continue
                else:
                    self.logger.error(f"获取结果失败: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"获取结果时网络错误: {e}")
        
        return CrawlResult(url=url, success=False, error_message="Task timeout or failed")
    
    def _parse_completed_result(self, result: Dict, url: str) -> CrawlResult:
        """解析完成的爬取结果"""
        results = result.get("results", [])
        if results and len(results) > 0:
            crawl_data = results[0]
            return CrawlResult(
                url=url,
                success=True,
                html=crawl_data.get("html", ""),
                markdown=crawl_data.get("markdown", ""),
                cleaned_html=crawl_data.get("cleaned_html", "")
            )
        else:
            return CrawlResult(url=url, success=False, error_message="No results in completed task")


class PreciseTableClassifier:
    """精确的表格样式分类器"""
    
    def __init__(self):
        self.categories = ["无表格线", "有表格线"]
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('PreciseTableClassifier')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def classify(self, crawl_result: CrawlResult) -> str:
        """基于表格的实际视觉特征进行分类"""
        if not crawl_result.success:
            return "未分类"
        
        soup = BeautifulSoup(crawl_result.html, 'html.parser')
        
        # 分析表格的视觉特征
        table_features = self._analyze_table_visual_features(soup)
        
        # 根据特征进行分类
        if self._has_complex_table_layout(table_features):
            classification = "有表格线"
        else:
            classification = "无表格线"
        
        self.logger.info(f"表格特征: {table_features}, 分类: {classification}")
        return classification
    
    def _analyze_table_visual_features(self, soup) -> Dict:
        """分析表格的视觉特征"""
        features = {
            "has_multi_column_data_table": False,
            "has_grid_layout": False,
            "has_structured_data": False,
            "max_data_columns": 0,
            "data_table_count": 0,
            "simple_list_count": 0
        }
        
        tables = soup.find_all('table')
        
        for table in tables:
            # 分析表格的数据结构
            table_analysis = self._analyze_single_table(table)
            
            if table_analysis["is_data_table"]:
                features["data_table_count"] += 1
                features["max_data_columns"] = max(
                    features["max_data_columns"], 
                    table_analysis["max_columns"]
                )
                
                if table_analysis["max_columns"] > 2:
                    features["has_multi_column_data_table"] = True
                
                if table_analysis["has_grid_structure"]:
                    features["has_grid_layout"] = True
                
                if table_analysis["has_structured_content"]:
                    features["has_structured_data"] = True
            else:
                features["simple_list_count"] += 1
        
        return features
    
    def _analyze_single_table(self, table) -> Dict:
        """分析单个表格的特征"""
        analysis = {
            "is_data_table": False,
            "max_columns": 0,
            "has_grid_structure": False,
            "has_structured_content": False
        }
        
        rows = table.find_all('tr')
        if not rows:
            return analysis
        
        # 分析表格的列结构
        column_counts = []
        for row in rows:
            cells = row.find_all(['td', 'th'])
            if cells:
                column_counts.append(len(cells))
        
        if not column_counts:
            return analysis
        
        analysis["max_columns"] = max(column_counts)
        
        # 判断是否为数据表格
        # 1. 多列且列数一致
        if len(set(column_counts)) <= 2 and analysis["max_columns"] > 2:
            analysis["is_data_table"] = True
            analysis["has_grid_structure"] = True
        
        # 2. 检查内容结构
        if self._has_structured_table_content(table):
            analysis["has_structured_content"] = True
            if analysis["max_columns"] > 1:
                analysis["is_data_table"] = True
        
        return analysis
    
    def _has_structured_table_content(self, table) -> bool:
        """检查表格是否有结构化内容"""
        rows = table.find_all('tr')
        if len(rows) < 2:
            return False
        
        # 检查是否有表头
        first_row = rows[0]
        if first_row.find('th'):
            return True
        
        # 检查是否有多列数据
        data_rows = 0
        for row in rows:
            cells = row.find_all(['td', 'th'])
            if len(cells) > 2:
                # 检查单元格内容是否像数据
                cell_texts = [cell.get_text(strip=True) for cell in cells]
                non_empty_cells = [text for text in cell_texts if text]
                if len(non_empty_cells) > 2:
                    data_rows += 1
        
        # 如果有多行数据，认为是结构化表格
        return data_rows > 1
    
    def _has_complex_table_layout(self, features: Dict) -> bool:
        """判断是否为复杂表格布局（有表格线）"""
        # 基于图片示例的特征进行判断
        
        # 1. 如果有多列数据表格，判断为有表格线
        if features["has_multi_column_data_table"]:
            return True
        
        # 2. 如果有网格布局，判断为有表格线
        if features["has_grid_layout"]:
            return True
        
        # 3. 如果有结构化数据且最大列数超过3，判断为有表格线
        if features["has_structured_data"] and features["max_data_columns"] > 3:
            return True
        
        # 4. 如果数据表格数量较多，判断为有表格线
        if features["data_table_count"] > 2:
            return True
        
        return False
    
    def get_categories(self) -> List[str]:
        """获取所有可能的分类"""
        return self.categories


class PreciseTableCrawlerSystem:
    """精确的表格分类爬虫系统主类"""
    
    def __init__(self, config: CrawlConfig = None):
        self.config = config or CrawlConfig()
        self.crawler = WebCrawler(self.config)
        self.classifier = PreciseTableClassifier()
        self.link_extractor = LinkExtractor()
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('PreciseTableCrawlerSystem')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def extract_and_classify_links(self, source_url: str, output_file: str) -> None:
        """从源URL提取链接并进行精确的表格分类"""
        self.logger.info(f"开始处理源URL: {source_url}")
        
        # 1. 爬取源页面
        source_result = self.crawler.crawl_url(source_url)
        if not source_result.success:
            self.logger.error(f"无法爬取源页面: {source_result.error_message}")
            return
        
        # 2. 提取链接
        links = self.link_extractor.extract_links_from_html(source_result.html, source_url)
        self.logger.info(f"从源页面提取到 {len(links)} 个链接")
        
        if not links:
            self.logger.warning("未找到任何有效链接")
            return
        
        # 3. 爬取和分类每个链接
        results = []
        for i, link in enumerate(links, 1):
            self.logger.info(f"处理第 {i}/{len(links)} 个链接: {link['text']}")
            
            # 爬取链接
            crawl_result = self.crawler.crawl_url(link['url'])
            
            # 分类
            if crawl_result.success:
                classification = self.classifier.classify(crawl_result)
                self.logger.info(f"分类结果: {classification}")
            else:
                classification = "爬取失败"
                self.logger.error(f"爬取失败: {crawl_result.error_message}")
            
            results.append({
                '单位名称': link['text'],
                '子链接': link['url'],
                '分类': classification
            })
            
            # 添加延迟避免过于频繁的请求
            time.sleep(self.config.rate_limit_delay)
        
        # 4. 保存结果
        self._save_results(results, output_file)
        self._print_summary(results)
    
    def _save_results(self, results: List[Dict], output_file: str) -> None:
        """保存结果到CSV文件"""
        try:
            df = pd.DataFrame(results)
            df.to_csv(output_file, index=False, encoding='utf-8')
            self.logger.info(f"结果已保存到: {output_file}")
        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
    
    def _print_summary(self, results: List[Dict]) -> None:
        """打印处理摘要"""
        total = len(results)
        categories = {}
        
        for result in results:
            category = result['分类']
            categories[category] = categories.get(category, 0) + 1
        
        self.logger.info(f"\n📊 精确表格分类摘要:")
        self.logger.info(f"总记录数: {total}")
        for category, count in categories.items():
            percentage = (count / total) * 100 if total > 0 else 0
            self.logger.info(f"  {category}: {count} 个 ({percentage:.1f}%)")


def main():
    """主函数"""
    print("🚀 精确的表格样式分类爬虫系统")
    print("=" * 50)
    print("基于实际表格视觉特征进行分类：")
    print("  - 第一类：无表格线（简单列表样式）")
    print("  - 第二类：有表格线（复杂网格表格）")
    print()
    
    # 配置系统
    config = CrawlConfig()
    system = PreciseTableCrawlerSystem(config)
    
    # 使用默认参数
    source_url = "http://172.18.1.16/phone/20201227.html"
    output_file = "final_units_data.csv"
    
    print(f"源URL: {source_url}")
    print(f"输出文件: {output_file}")
    print()
    
    # 开始处理
    try:
        system.extract_and_classify_links(source_url, output_file)
        print("\n✅ 处理完成！")
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")


if __name__ == "__main__":
    main()
