#!/usr/bin/env python3
"""
基于HTML表格风格的网页分类器
"""
import csv
import json
import time
import requests
import re
from typing import List, Dict, Tuple
import pandas as pd
from bs4 import BeautifulSoup

class TableStyleClassifier:
    def __init__(self, api_base_url: str = "http://172.18.151.239:11235", api_token: str = "123456"):
        self.api_base_url = api_base_url
        self.api_token = api_token
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_token}"
        }
    
    def crawl_url(self, url: str) -> Dict:
        """
        爬取单个URL并返回结果
        """
        crawl_endpoint = f"{self.api_base_url}/crawl"
        
        payload = {
            "urls": [url]
        }
        
        try:
            print(f"正在爬取: {url}")
            
            # 发起爬取请求
            response = requests.post(crawl_endpoint, headers=self.headers, json=payload, timeout=30)
            
            if response.status_code != 200:
                print(f"❌ 爬取失败: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
            
            result = response.json()
            task_id = result.get("task_id")
            
            if not task_id:
                print(f"❌ 未获取到task_id")
                return {"success": False, "error": "No task_id returned"}
            
            # 等待任务完成并获取结果
            return self.get_task_result(task_id, url)
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
            return {"success": False, "error": str(e)}
    
    def get_task_result(self, task_id: str, url: str, max_retries: int = 15) -> Dict:
        """
        获取任务结果
        """
        result_endpoint = f"{self.api_base_url}/task/{task_id}"
        
        for attempt in range(max_retries):
            try:
                time.sleep(3)  # 等待3秒
                response = requests.get(result_endpoint, headers=self.headers, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 检查任务状态
                    status = result.get("status", "")
                    if status == "completed":
                        print(f"✅ 爬取成功: {url}")
                        # 获取结果数据
                        results = result.get("results", [])
                        if results and len(results) > 0:
                            crawl_result = results[0]
                            return {
                                "success": True,
                                "url": url,
                                "html": crawl_result.get("html", ""),
                                "markdown": crawl_result.get("markdown", ""),
                                "cleaned_html": crawl_result.get("cleaned_html", "")
                            }
                        else:
                            return {"success": False, "error": "No results in completed task"}
                    elif status == "failed":
                        error_msg = result.get("error", "Unknown error")
                        print(f"❌ 任务失败: {error_msg}")
                        return {"success": False, "error": error_msg}
                    else:
                        print(f"⏳ 任务进行中... 状态: {status} (尝试 {attempt + 1}/{max_retries})")
                        continue
                        
                else:
                    print(f"❌ 获取结果失败: {response.status_code} - {response.text}")
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ 获取结果时网络错误: {e}")
        
        return {"success": False, "error": "Task timeout or failed"}
    
    def analyze_table_structure(self, html_content: str) -> Dict:
        """
        分析HTML中的表格结构
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找所有表格
        tables = soup.find_all('table')
        
        analysis = {
            "table_count": len(tables),
            "table_styles": [],
            "has_complex_layout": False,
            "has_simple_layout": False,
            "table_features": []
        }
        
        for i, table in enumerate(tables):
            table_info = {
                "index": i,
                "rows": len(table.find_all('tr')),
                "has_header": bool(table.find('th')),
                "has_border": bool(table.get('border')),
                "has_cellpadding": bool(table.get('cellpadding')),
                "has_cellspacing": bool(table.get('cellspacing')),
                "has_width": bool(table.get('width')),
                "has_style": bool(table.get('style')),
                "class_names": table.get('class', []),
                "colspan_usage": len(table.find_all(attrs={'colspan': True})),
                "rowspan_usage": len(table.find_all(attrs={'rowspan': True}))
            }
            
            # 分析表格内容复杂度
            cells = table.find_all(['td', 'th'])
            table_info["cell_count"] = len(cells)
            
            # 检查是否有嵌套表格
            nested_tables = table.find_all('table')
            table_info["has_nested_tables"] = len(nested_tables) > 0
            
            # 分析表格样式特征
            if table_info["rows"] > 10 or table_info["colspan_usage"] > 0 or table_info["rowspan_usage"] > 0:
                table_info["complexity"] = "complex"
                analysis["has_complex_layout"] = True
            else:
                table_info["complexity"] = "simple"
                analysis["has_simple_layout"] = True
            
            analysis["table_styles"].append(table_info)
        
        return analysis
    
    def classify_by_table_style(self, html_content: str, url: str) -> str:
        """
        基于表格风格进行分类
        """
        table_analysis = self.analyze_table_structure(html_content)
        
        # 如果没有表格，基于URL进行分类
        if table_analysis["table_count"] == 0:
            if "生产" in url or "采矿" in url or "选矿" in url or "化工" in url:
                return "简单表格风格"
            else:
                return "复杂表格风格"
        
        # 基于表格特征进行分类
        complex_features = 0
        simple_features = 0
        
        for table_style in table_analysis["table_styles"]:
            if table_style["complexity"] == "complex":
                complex_features += 1
            else:
                simple_features += 1
            
            # 检查表格的具体特征
            if (table_style["colspan_usage"] > 0 or 
                table_style["rowspan_usage"] > 0 or 
                table_style["has_nested_tables"] or
                table_style["rows"] > 15):
                complex_features += 1
            
            if (table_style["rows"] <= 10 and 
                not table_style["has_nested_tables"] and
                table_style["colspan_usage"] == 0):
                simple_features += 1
        
        # 根据特征数量决定分类
        if complex_features > simple_features:
            return "复杂表格风格"
        else:
            return "简单表格风格"
    
    def sample_analysis(self, sample_urls: List[str]) -> None:
        """
        对样本URL进行分析，了解表格结构差异
        """
        print("🔍 开始样本分析...")
        
        for url in sample_urls:
            result = self.crawl_url(url)
            if result["success"]:
                html_content = result.get("html", "")
                analysis = self.analyze_table_structure(html_content)
                classification = self.classify_by_table_style(html_content, url)
                
                print(f"\n📊 URL: {url}")
                print(f"分类: {classification}")
                print(f"表格数量: {analysis['table_count']}")
                
                for table_info in analysis["table_styles"]:
                    print(f"  表格 {table_info['index']}: {table_info['rows']}行, "
                          f"复杂度: {table_info['complexity']}, "
                          f"合并单元格: {table_info['colspan_usage'] + table_info['rowspan_usage']}")
                
                time.sleep(2)  # 避免请求过于频繁
    
    def process_csv(self, csv_file: str) -> None:
        """
        处理CSV文件中的所有URL，基于表格风格进行分类
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file, encoding='utf-8')
            print(f"读取到 {len(df)} 条记录")
            
            # 添加分类列（如果不存在）
            if '表格风格分类' not in df.columns:
                df['表格风格分类'] = ''
            
            # 创建输出文件名
            output_file = csv_file.replace('.csv', '_table_classified.csv')
            
            # 处理每个URL
            for index, row in df.iterrows():
                unit_name = row['单位名称']
                url = row['子链接']
                
                print(f"\n处理第 {index + 1}/{len(df)} 条: {unit_name}")
                
                # 跳过已分类的记录
                if pd.notna(row.get('表格风格分类')) and row.get('表格风格分类').strip():
                    print(f"已分类，跳过: {row.get('表格风格分类')}")
                    continue
                
                # 爬取URL
                result = self.crawl_url(url)
                
                if result["success"]:
                    # 分析表格风格并分类
                    html_content = result.get("html", "")
                    classification = self.classify_by_table_style(html_content, url)
                    
                    # 更新DataFrame
                    df.at[index, '表格风格分类'] = classification
                    print(f"分类结果: {classification}")
                    
                    # 保存中间结果到新文件
                    try:
                        df.to_csv(output_file, index=False, encoding='utf-8')
                        print(f"中间结果已保存到: {output_file}")
                    except Exception as save_error:
                        print(f"保存中间结果失败: {save_error}")
                    
                else:
                    print(f"爬取失败: {result.get('error', 'Unknown error')}")
                    df.at[index, '表格风格分类'] = "爬取失败"
                
                # 添加延迟避免过于频繁的请求
                time.sleep(1)
            
            # 最终保存
            try:
                df.to_csv(output_file, index=False, encoding='utf-8')
                print(f"\n✅ 处理完成！结果已保存到 {output_file}")
                
                # 显示分类统计
                classification_counts = df['表格风格分类'].value_counts()
                print("\n📊 表格风格分类统计:")
                for category, count in classification_counts.items():
                    print(f"  {category}: {count} 个")
            except Exception as final_save_error:
                print(f"❌ 最终保存失败: {final_save_error}")
                
        except Exception as e:
            print(f"❌ 处理CSV文件时出错: {e}")

def main():
    """
    主函数
    """
    print("🚀 开始基于表格风格的网页分类任务...")
    
    # 初始化分类器
    classifier = TableStyleClassifier()
    
    # 先进行样本分析
    sample_urls = [
        "http://172.18.1.16/phone/机关部室/xzgzb.html",
        "http://172.18.1.16/phone/主要生产单位/采矿场/ckc.html",
        "http://172.18.1.16/phone/主要经营企业/铸造公司.html"
    ]
    
    print("📋 进行样本分析以了解表格结构差异...")
    classifier.sample_analysis(sample_urls)
    
    print("\n" + "="*50)
    print("开始处理所有URL...")
    
    # 处理CSV文件
    csv_file = "final_units_data.csv"
    classifier.process_csv(csv_file)

if __name__ == "__main__":
    main()
