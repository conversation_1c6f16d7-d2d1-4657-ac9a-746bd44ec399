#!/usr/bin/env python3
"""
测试crawl4ai API连接
"""
import requests
import json

def test_crawl4ai_connection():
    """测试crawl4ai API是否正常工作"""
    api_url = "http://172.18.151.239:11235/crawl"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer 123456"
    }
    
    # 测试数据
    test_data = {
        "urls": ["http://example.com"],
        "extraction_config": {
            "type": "cosine",
            "params": {
                "semantic_filter": "main content"
            }
        }
    }
    
    try:
        print("正在测试crawl4ai API连接...")
        response = requests.post(api_url, headers=headers, json=test_data, timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ API连接成功!")
            result = response.json()
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ API连接失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接错误: {e}")
        return False

if __name__ == "__main__":
    test_crawl4ai_connection()
