#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的问题
1. 方法一：无表格线页面保留数字（如"105"）
2. 方法二：有表格线页面严格按照fully_universal_crawler.py逻辑
"""

import asyncio
import csv
from datetime import datetime
from base_crawler import BaseCrawler, CrawlConfig
from phone_extractor import PhoneExtractor
from csv_processor import CSVProcessor


async def test_method_one_number_preservation():
    """测试方法一：数字保留功能"""
    print("🎯 测试方法一：无表格线页面数字保留功能")
    print("=" * 60)
    
    # 加载CSV数据
    csv_processor = CSVProcessor()
    if not csv_processor.load_data():
        print("❌ CSV数据加载失败")
        return []
    
    # 选择几个无表格线的单位进行测试
    test_units = ["机动能源部", "行政工作部", "矿纪委"]
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    all_records = []
    
    for i, unit_name in enumerate(test_units, 1):
        print(f"\n📋 测试第 {i}/{len(test_units)} 个单位: {unit_name}")
        
        # 获取单位记录
        record = csv_processor.get_unit_record(unit_name)
        if not record:
            print(f"❌ 未找到单位: {unit_name}")
            continue
        
        print(f"  🔗 链接: {record.sub_link}")
        print(f"  📊 分类: {record.classification}")
        
        try:
            # 爬取页面
            html_content = await crawler.crawl_url(record.sub_link)
            if not html_content:
                print(f"❌ 爬取失败: {unit_name}")
                continue
            
            # 提取电话信息
            phone_records = extractor.extract_phone_info(
                html_content,
                record.unit_name,
                record.classification
            )
            
            if phone_records:
                all_records.extend(phone_records)
                print(f"  ✅ 提取到 {len(phone_records)} 条电话记录:")
                
                # 检查是否有包含数字的用户名
                number_preserved_count = 0
                for j, phone_record in enumerate(phone_records, 1):
                    has_number = any(char.isdigit() for char in phone_record.name)
                    if has_number:
                        number_preserved_count += 1
                        print(f"    {j}. {phone_record.phone} - {phone_record.name} ✅ (包含数字)")
                    else:
                        print(f"    {j}. {phone_record.phone} - {phone_record.name}")
                
                print(f"  📊 包含数字的用户名: {number_preserved_count}/{len(phone_records)} 条")
            else:
                print(f"  ⚠️ 未提取到电话信息")
            
            # 添加延迟
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"❌ 处理异常: {e}")
    
    return all_records


async def test_method_two_exact_logic():
    """测试方法二：严格按照fully_universal_crawler.py逻辑"""
    print(f"\n🎯 测试方法二：有表格线页面严格逻辑")
    print("=" * 60)
    
    # 直接测试采矿场（fully_universal_crawler.py验证过的成功案例）
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    
    test_url = "http://172.18.1.16/phone/主要生产单位/采矿场/ckc.html"
    print(f"🌐 测试URL: {test_url}")
    
    html_content = await crawler.crawl_url(test_url)
    if not html_content:
        print("❌ 页面爬取失败")
        return []
    
    print(f"✅ 页面爬取成功，内容长度: {len(html_content)}")
    
    # 使用修复后的提取器
    records = extractor.extract_phone_info(html_content, "采矿场", "有表格线")
    
    print(f"✅ 采矿场提取到 {len(records)} 条电话记录")
    
    # 检查数据质量
    error_count = 0
    valid_count = 0
    
    print(f"\n📊 数据质量检查:")
    for i, record in enumerate(records[:20], 1):  # 检查前20条
        # 检查用户名是否包含电话号码
        name_has_phone = any(re.search(r'\d{7,11}', record.name) for _ in [None])
        name_has_phone = bool(re.search(r'\d{7,11}', record.name))
        
        # 检查单位名称是否包含日期
        unit_has_date = any(keyword in record.unit for keyword in ['年', '月', '日', '星期'])
        
        if name_has_phone or unit_has_date:
            error_count += 1
            print(f"  ❌ {i}. {record.phone} - {record.name} ({record.unit})")
            if name_has_phone:
                print(f"      问题：用户名包含电话号码")
            if unit_has_date:
                print(f"      问题：单位名称包含日期")
        else:
            valid_count += 1
            if i <= 10:  # 只显示前10条正确的
                print(f"  ✅ {i}. {record.phone} - {record.name} ({record.unit})")
    
    print(f"\n📈 质量统计:")
    print(f"  正确记录: {valid_count} 条")
    print(f"  错误记录: {error_count} 条")
    print(f"  正确率: {valid_count/(valid_count+error_count)*100:.1f}%")
    
    return records


def save_fixed_test_results(method_one_records, method_two_records):
    """保存修复测试结果"""
    print(f"\n🎯 保存修复测试结果")
    print("=" * 60)
    
    # 合并所有记录
    all_records = method_one_records + method_two_records
    
    if not all_records:
        print("⚠️ 没有数据可保存")
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存CSV文件
    csv_filename = f"修复问题测试结果_{timestamp}.csv"
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['电话号码', '单位名称', '电话用户名', '提取方法', '测试方法']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            # 方法一记录
            for record in method_one_records:
                writer.writerow({
                    '电话号码': record.phone,
                    '单位名称': record.unit,
                    '电话用户名': record.name,
                    '提取方法': record.extraction_method,
                    '测试方法': '方法一（无表格线）'
                })
            
            # 方法二记录
            for record in method_two_records:
                writer.writerow({
                    '电话号码': record.phone,
                    '单位名称': record.unit,
                    '电话用户名': record.name,
                    '提取方法': record.extraction_method,
                    '测试方法': '方法二（有表格线）'
                })
        
        print(f"✅ CSV文件保存成功: {csv_filename}")
    except Exception as e:
        print(f"❌ CSV文件保存失败: {e}")
    
    # 显示统计信息
    print(f"\n📊 修复测试结果统计:")
    print(f"  方法一（无表格线）: {len(method_one_records)} 条")
    print(f"  方法二（有表格线）: {len(method_two_records)} 条")
    print(f"  总计: {len(all_records)} 条")
    
    # 方法一数字保留统计
    if method_one_records:
        number_preserved = sum(1 for record in method_one_records 
                             if any(char.isdigit() for char in record.name))
        print(f"  方法一数字保留: {number_preserved}/{len(method_one_records)} 条")
    
    # 方法二质量统计
    if method_two_records:
        import re
        error_count = 0
        for record in method_two_records:
            name_has_phone = bool(re.search(r'\d{7,11}', record.name))
            unit_has_date = any(keyword in record.unit for keyword in ['年', '月', '日', '星期'])
            if name_has_phone or unit_has_date:
                error_count += 1
        
        valid_count = len(method_two_records) - error_count
        print(f"  方法二数据质量: {valid_count}/{len(method_two_records)} 条正确 ({valid_count/len(method_two_records)*100:.1f}%)")


async def main():
    """主测试函数"""
    print("🕷️ 修复问题测试程序")
    print("测试方法一数字保留和方法二严格逻辑")
    print("=" * 70)
    
    try:
        # 测试方法一：数字保留功能
        method_one_records = await test_method_one_number_preservation()
        
        # 测试方法二：严格按照fully_universal_crawler.py逻辑
        method_two_records = await test_method_two_exact_logic()
        
        # 保存测试结果
        save_fixed_test_results(method_one_records, method_two_records)
        
        print(f"\n🎉 修复问题测试完成！")
        print("=" * 70)
        print("✅ 修复验证:")
        print(f"  - 方法一数字保留: {'✅ 修复成功' if any(any(char.isdigit() for char in r.name) for r in method_one_records) else '❌ 仍有问题'}")
        print(f"  - 方法二严格逻辑: {'✅ 修复成功' if method_two_records else '❌ 仍有问题'}")
        print(f"  - 数据质量改善: {'✅ 显著改善' if method_two_records else '❌ 无改善'}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    import re
    asyncio.run(main())
