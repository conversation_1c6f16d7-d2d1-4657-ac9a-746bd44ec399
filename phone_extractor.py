#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电话信息提取器模块
支持有表格线和无表格线两种网页样式的电话信息提取
"""

import re
import logging
from typing import List, Dict, Optional
from bs4 import BeautifulSoup
from dataclasses import dataclass


@dataclass
class PhoneRecord:
    """电话记录数据类"""
    phone: str
    unit: str
    name: str
    extraction_method: str = ""  # 记录提取方法，便于调试


class PhoneExtractor:
    """
    通用电话信息提取器
    
    支持两种网页样式：
    1. 有表格线：使用表格结构提取
    2. 无表格线：使用文本模式提取
    """
    
    def __init__(self):
        """初始化提取器"""
        self.logger = self._setup_logger()
        # 电话号码正则表达式：匹配7位内线或11位手机号
        self.phone_pattern = r'\b(?:\d{7}|\d{11})\b'
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def extract_phone_info(self, html_content: str, unit_name: str, 
                          page_type: str = "auto") -> List[PhoneRecord]:
        """
        从HTML内容中提取电话信息
        
        Args:
            html_content: 网页HTML内容
            unit_name: 单位名称
            page_type: 页面类型 ("有表格线", "无表格线", "auto")
            
        Returns:
            List[PhoneRecord]: 提取的电话记录列表
        """
        self.logger.info(f"🔍 开始提取电话信息 - 单位: {unit_name}, 类型: {page_type}")
        
        if page_type == "auto":
            page_type = self._detect_page_type(html_content)
            self.logger.info(f"🤖 自动检测页面类型: {page_type}")
        
        if page_type == "有表格线":
            return self._extract_from_table(html_content, unit_name)
        else:
            return self._extract_from_text(html_content, unit_name)
    
    def _detect_page_type(self, html_content: str) -> str:
        """
        自动检测页面类型
        
        Args:
            html_content: HTML内容
            
        Returns:
            str: "有表格线" 或 "无表格线"
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找带边框的表格
        tables_with_border = soup.find_all('table', {'border': '1'})
        if tables_with_border:
            # 检查表格是否包含电话信息
            for table in tables_with_border:
                if self._table_has_phone_data(table):
                    return "有表格线"
        
        return "无表格线"
    
    def _table_has_phone_data(self, table) -> bool:
        """检查表格是否包含电话数据"""
        table_text = table.get_text()
        # 检查是否包含电话号码
        phones = re.findall(self.phone_pattern, table_text)
        return len(phones) > 0
    
    def _extract_from_table(self, html_content: str, unit_name: str) -> List[PhoneRecord]:
        """
        从有表格线的页面提取电话信息
        完全按照fully_universal_crawler.py的成功逻辑实现
        """
        self.logger.info("📊 使用表格模式提取电话信息（基于fully_universal_crawler.py）")

        # 使用fully_universal_crawler.py的完全通用提取方法
        phone_data = self._extract_phone_data_fully_universal(html_content, unit_name)

        # 转换为PhoneRecord格式
        phone_records = []
        for data in phone_data:
            record = PhoneRecord(
                phone=data['电话号码'],
                unit=data['单位名称'],
                name=data['电话用户名'],
                extraction_method="表格模式（fully_universal）"
            )
            phone_records.append(record)

        self.logger.info(f"✅ 表格模式提取完成，共 {len(phone_records)} 条记录")
        return phone_records

    def _extract_phone_data_fully_universal(self, html_content: str, base_unit_name: str) -> List[Dict[str, str]]:
        """
        完全通用的电话数据提取方法
        严格按照fully_universal_crawler.py的精确逻辑实现
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        phone_data = []

        # 查找所有带边框的表格（严格按照原程序）
        tables = soup.find_all('table', {'border': '1'})
        self.logger.info(f"📋 发现 {len(tables)} 个带边框表格")

        # 如果没有找到带边框的表格，尝试智能识别
        # 这是为了兼容crawl4ai清理HTML属性的情况
        if not tables:
            self.logger.info(f"📋 未找到带边框表格，尝试智能识别...")
            all_tables = soup.find_all('table')

            # 根据标准程序的表格特征进行筛选
            for i, table in enumerate(all_tables):
                if self._is_target_table_exact(table):
                    tables.append(table)
                    self.logger.info(f"    表格 {i}: 符合目标表格特征")
                else:
                    self.logger.info(f"    表格 {i}: 不符合目标表格特征")

            self.logger.info(f"📋 智能识别发现 {len(tables)} 个目标表格")

            if not tables:
                return []

        # 用于存储已找到的第二层单位名称，供后续表格复用
        last_found_second_level = None

        for table_idx, table in enumerate(tables):
            self.logger.info(f"🔍 处理表格 {table_idx}:")

            # 动态获取表格上方的第二层单位名称
            second_level_name = self._get_second_level_name_from_table_exact(table, soup)

            if second_level_name:
                self.logger.info(f"  找到第二层单位名称: {second_level_name}")
                last_found_second_level = second_level_name
            else:
                # 如果当前表格没有找到，使用最近找到的第二层单位名称
                if last_found_second_level:
                    second_level_name = last_found_second_level
                    self.logger.info(f"  使用最近的第二层单位名称: {second_level_name}")
                else:
                    self.logger.info(f"  未找到任何第二层单位名称，将使用两层结构")

            # 检查表格是否包含电话信息
            if not self._has_phone_data_exact(table):
                self.logger.info(f"  跳过：表格不包含电话信息")
                continue

            # 提取表格数据
            table_data = self._extract_table_data_universal_exact(table, base_unit_name, second_level_name)
            phone_data.extend(table_data)

        return phone_data

    def _get_second_level_name_from_table_exact(self, table, soup) -> Optional[str]:
        """
        动态获取表格上方的第二层单位名称
        严格按照fully_universal_crawler.py的精确逻辑，只识别有效的第二层单位
        """
        # 定义有效的第二层单位名称（严格按照标准程序）
        valid_second_level_names = ['场机关', '生产现场']

        # 方法1：查找表格前面紧挨着的文本元素
        current = table.previous_sibling

        # 向前查找最多10个兄弟元素
        search_count = 0
        while current and search_count < 10:
            if hasattr(current, 'get_text'):
                text = current.get_text(strip=True)

                # 检查是否为有效的第二层单位名称
                if text in valid_second_level_names:
                    self.logger.info(f"    找到第二层单位名称: {text}")
                    return text

            current = current.previous_sibling
            search_count += 1

        # 方法2：查找表格所在的父元素中的文本
        parent = table.parent
        if parent:
            # 查找父元素中表格前面的文本
            for child in parent.children:
                if child == table:
                    break
                if hasattr(child, 'get_text'):
                    text = child.get_text(strip=True)
                    if text in valid_second_level_names:
                        self.logger.info(f"    从父元素找到第二层单位名称: {text}")
                        return text

        # 方法3：查找表格前面的所有文本节点（限制在更近的范围内）
        all_text_before = []
        for element in soup.find_all(text=True):
            if element.parent and element.parent.name not in ['script', 'style']:
                text = element.strip()
                if text in valid_second_level_names:
                    # 检查这个文本是否在表格之前且距离很近
                    try:
                        element_pos = str(soup).find(str(element))
                        table_pos = str(soup).find(str(table))
                        distance = table_pos - element_pos
                        # 只考虑距离很近的文本（500字符以内），避免找到其他表格的标题
                        if element_pos < table_pos and distance < 500:
                            all_text_before.append((text, distance))
                    except:
                        continue

        # 选择距离表格最近的文本
        if all_text_before:
            all_text_before.sort(key=lambda x: x[1])  # 按距离排序
            closest_text = all_text_before[0][0]
            self.logger.info(f"    从全局搜索找到第二层单位名称: {closest_text}")
            return closest_text

        # 如果没找到，返回None，表示没有第二层单位名称
        return None

    def _is_target_table_exact(self, table) -> bool:
        """
        智能识别目标表格（兼容crawl4ai清理HTML属性的情况）
        基于标准程序中带border='1'表格的特征
        """
        # 检查表格基本结构
        rows = table.find_all('tr')
        if len(rows) < 2 or len(rows) > 20:  # 行数应该在合理范围内
            return False

        # 检查列数（标准表格都是4列）
        header_row = rows[0]
        header_cells = header_row.find_all(['td', 'th'])
        if len(header_cells) != 4:  # 必须是4列
            return False

        # 检查表格属性（如果有width='100%'更好）
        attrs = table.attrs
        if 'width' in attrs and attrs['width'] == '100%':
            # 这很可能是目标表格
            pass

        # 检查列头是否为简洁的部门名称
        headers = [cell.get_text(strip=True) for cell in header_cells]

        # 过滤掉包含大量文本的列头
        for header in headers:
            if len(header) > 50:  # 列头过长
                return False
            if header.count(':') > 2:  # 包含太多冒号
                return False
            if len(re.findall(r'\d{7,11}', header)) > 2:  # 包含太多电话号码
                return False

        # 检查是否包含电话信息
        if not self._has_phone_data_exact(table):
            return False

        return True

    def _has_phone_data_exact(self, table) -> bool:
        """
        检查表格是否包含电话数据
        严格按照fully_universal_crawler.py的逻辑
        """
        table_text = table.get_text()
        return bool(re.search(r'\b(\d{7}|\d{11})\b', table_text))

    def _extract_table_data_universal_exact(self, table, base_unit_name: str, second_level_name: Optional[str]) -> List[Dict[str, str]]:
        """
        通用表格数据提取方法
        严格按照fully_universal_crawler.py的extract_table_data_universal逻辑
        """
        phone_data = []
        rows = table.find_all('tr')

        if len(rows) <= 1:
            return phone_data

        # 第一行是列头
        header_row = rows[0]
        header_cells = header_row.find_all(['td', 'th'])

        # 提取列头名称
        column_headers = []
        for cell in header_cells:
            header_text = cell.get_text(strip=True)
            # 移除HTML标签如<strong>
            clean_header = re.sub(r'<[^>]+>', '', header_text)
            column_headers.append(clean_header)

        self.logger.info(f"    识别到列头: {column_headers}")

        # 处理数据行
        data_rows = rows[1:]

        # 按列处理数据
        for col_idx, column_header in enumerate(column_headers):
            if not column_header:
                continue

            self.logger.info(f"    处理列 {col_idx}: {column_header}")

            # 提取该列的所有数据
            col_data = self._extract_column_data_new_exact(data_rows, col_idx, base_unit_name, second_level_name, column_header)
            phone_data.extend(col_data)

        return phone_data

    def _extract_column_data_new_exact(self, data_rows, col_idx: int, base_unit_name: str,
                                      second_level_name: Optional[str], column_header: str) -> List[Dict[str, str]]:
        """
        完全通用的列数据提取方法，动态识别子列
        严格按照fully_universal_crawler.py的extract_column_data_new逻辑
        """
        phone_data = []
        row_idx = 0

        while row_idx < len(data_rows):
            row = data_rows[row_idx]
            cells = row.find_all(['td', 'th'])

            if col_idx >= len(cells):
                row_idx += 1
                continue

            cell = cells[col_idx]
            cell_text = cell.get_text(strip=True)

            # 跳过空单元格
            if not cell_text or cell_text == '&nbsp;':
                row_idx += 1
                continue

            # 检查是否为子列标题（不以7位或11位数字结尾的文字）
            if self._is_sub_column_title_exact(cell_text):
                # 这是一个子列标题
                sub_column_name = self._extract_sub_column_name_exact(cell_text)
                self.logger.info(f"      发现子列: {sub_column_name}")

                # 提取该子列下的所有电话数据
                sub_data, next_row_idx = self._extract_sub_column_phone_data_exact(
                    data_rows, row_idx + 1, col_idx, base_unit_name, second_level_name, sub_column_name
                )
                phone_data.extend(sub_data)
                row_idx = next_row_idx
            else:
                # 这是电话数据行，使用原始列头作为单位名称
                phone_matches = re.findall(r'\b(\d{7}|\d{11})\b', cell_text)

                for phone in phone_matches:
                    user_name = self._extract_user_name_universal_exact(cell_text, phone)

                    # 根据是否有第二层单位名称构建单位名称
                    if second_level_name:
                        unit_name = f"{base_unit_name}-{second_level_name}-{column_header}"
                    else:
                        unit_name = f"{base_unit_name}-{column_header}"

                    phone_data.append({
                        '电话号码': phone,
                        '电话用户名': user_name,
                        '单位名称': unit_name
                    })

                    self.logger.info(f"      找到: {phone} | {user_name} | {unit_name}")

                row_idx += 1

        return phone_data

    def _is_sub_column_title_exact(self, text: str) -> bool:
        """
        判断是否为子列标题
        严格按照fully_universal_crawler.py的逻辑
        """
        # 不以7位或11位数字结尾
        if re.search(r'\d{7}$|\d{11}$', text):
            return False

        # 不包含冒号（冒号通常表示电话号码行）
        if ':' in text or '：' in text:
            return False

        # 包含中文字符
        if not re.search(r'[\u4e00-\u9fa5]', text):
            return False

        # 长度合理（1-20个字符）
        if len(text) < 1 or len(text) > 20:
            return False

        # 不是纯数字
        if text.isdigit():
            return False

        return True

    def _extract_sub_column_name_exact(self, text: str) -> str:
        """
        提取子列名称
        严格按照fully_universal_crawler.py的逻辑
        """
        # 移除HTML标签和多余的空白
        clean_text = re.sub(r'<[^>]+>', '', text).strip()

        # 提取中文字符和数字
        clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()]', '', clean_text)

        return clean_text if clean_text else text

    def _extract_sub_column_phone_data_exact(self, data_rows, start_row_idx: int, col_idx: int,
                                           base_unit_name: str, second_level_name: Optional[str],
                                           sub_column_name: str) -> tuple:
        """
        提取子列下的电话数据
        严格按照fully_universal_crawler.py的extract_sub_column_phone_data逻辑
        """
        phone_data = []
        row_idx = start_row_idx

        while row_idx < len(data_rows):
            row = data_rows[row_idx]
            cells = row.find_all(['td', 'th'])

            if col_idx >= len(cells):
                row_idx += 1
                continue

            cell = cells[col_idx]
            cell_text = cell.get_text(strip=True)

            # 如果遇到空单元格，跳过
            if not cell_text or cell_text == '&nbsp;':
                row_idx += 1
                continue

            # 如果遇到新的子列标题，结束当前子列
            if self._is_sub_column_title_exact(cell_text):
                break

            # 提取电话号码
            phone_matches = re.findall(r'\b(\d{7}|\d{11})\b', cell_text)

            for phone in phone_matches:
                user_name = self._extract_user_name_universal_exact(cell_text, phone)

                # 根据是否有第二层单位名称构建单位名称
                if second_level_name:
                    unit_name = f"{base_unit_name}-{second_level_name}-{sub_column_name}"
                else:
                    unit_name = f"{base_unit_name}-{sub_column_name}"

                phone_data.append({
                    '电话号码': phone,
                    '电话用户名': user_name,
                    '单位名称': unit_name
                })

                self.logger.info(f"        找到: {phone} | {user_name} | {unit_name}")

            row_idx += 1

        return phone_data, row_idx

    def _extract_user_name_universal_exact(self, cell_text: str, phone: str) -> str:
        """
        通用用户名提取方法
        严格按照fully_universal_crawler.py的extract_user_name_universal逻辑
        """
        # 清理文本
        cell_text = re.sub(r'\s+', ' ', cell_text.strip())

        # 方法1：查找电话号码前的内容
        phone_pattern = re.escape(phone)
        pattern1 = r'([^:：]+?)[\s]*[:：]\s*' + phone_pattern
        match1 = re.search(pattern1, cell_text)

        if match1:
            user_name = match1.group(1).strip()
            # 保留中文、英文、数字和常用符号
            user_name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()１２３４５６７８９０\-/]', '', user_name)
            if user_name:
                return user_name

        # 方法2：分割后查找
        parts = cell_text.split(phone)
        if len(parts) > 1:
            before_phone = parts[0].strip()
            before_phone = re.sub(r'[:：\s]+$', '', before_phone)
            cleaned_name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()１２３４５６７８９０\-/]', '', before_phone)
            if cleaned_name:
                return cleaned_name

        # 方法3：提取中文内容
        text_without_phone = cell_text.replace(phone, '')
        cleaned_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()１２３４５６７８９０\-/]', '', text_without_phone)
        if cleaned_text:
            return cleaned_text.strip()

        return "未知用户"
    
    def _get_second_level_name_from_table_universal(self, table, soup) -> Optional[str]:
        """
        动态获取表格上方的第二层单位名称
        基于fully_universal_crawler.py的完整逻辑
        """
        # 方法1：查找表格前面紧挨着的文本元素
        current = table.previous_sibling
        search_count = 0

        while current and search_count < 10:
            if hasattr(current, 'get_text'):
                text = current.get_text(strip=True)

                # 检查是否为8个中文字以内的有效单位名称
                if text and len(text) <= 16:  # 8个中文字约16个字符
                    # 检查是否包含中文且不包含电话号码和冒号
                    if (re.search(r'[\u4e00-\u9fa5]', text) and
                        not re.search(r'\d{7,11}', text) and
                        ':' not in text and '：' not in text):
                        # 过滤掉导航文本和无关文本
                        if not any(keyword in text for keyword in ['网站首页', '网上服务', '当前位置', '电话号码']):
                            self.logger.info(f"    找到第二层单位名称: {text}")
                            return text

            current = current.previous_sibling
            search_count += 1

        # 方法2：查找表格所在的父元素中的文本
        parent = table.parent
        if parent:
            # 查找父元素中表格前面的文本
            for child in parent.children:
                if child == table:
                    break
                if hasattr(child, 'get_text'):
                    text = child.get_text(strip=True)
                    if text and len(text) <= 16:
                        if (re.search(r'[\u4e00-\u9fa5]', text) and
                            not re.search(r'\d{7,11}', text) and
                            ':' not in text and '：' not in text):
                            if not any(keyword in text for keyword in ['网站首页', '网上服务', '当前位置', '电话号码']):
                                self.logger.info(f"    从父元素找到第二层单位名称: {text}")
                                return text

        # 方法3：查找表格前面的所有文本节点（限制在更近的范围内）
        all_text_before = []
        for element in soup.find_all(text=True):
            if element.parent and element.parent.name not in ['script', 'style']:
                text = element.strip()
                if text and len(text) <= 16:
                    if (re.search(r'[\u4e00-\u9fa5]', text) and
                        not re.search(r'\d{7,11}', text) and
                        ':' not in text and '：' not in text):
                        if not any(keyword in text for keyword in ['网站首页', '网上服务', '当前位置', '电话号码']):
                            # 检查这个文本是否在表格之前且距离很近
                            try:
                                element_pos = str(soup).find(str(element))
                                table_pos = str(soup).find(str(table))
                                distance = table_pos - element_pos
                                # 只考虑距离很近的文本（500字符以内），避免找到其他表格的标题
                                if element_pos < table_pos and distance < 500:
                                    all_text_before.append((text, distance))
                            except:
                                continue

        # 选择距离表格最近的文本
        if all_text_before:
            all_text_before.sort(key=lambda x: x[1])  # 按距离排序
            closest_text = all_text_before[0][0]
            self.logger.info(f"    从距离最近的文本找到第二层单位名称: {closest_text}")
            return closest_text

        return None

    def _is_phone_column_universal(self, column_header: str) -> bool:
        """
        判断是否为电话列
        完全按照fully_universal_crawler.py的逻辑
        """
        phone_keywords = ['电话', '联系方式', '办公室', '手机', '座机', '内线']
        return any(keyword in column_header for keyword in phone_keywords)

    def _extract_column_data_fully_universal(self, data_rows, col_idx: int, base_unit_name: str,
                                           second_level_name: Optional[str], column_header: str) -> List[Dict[str, str]]:
        """
        完全通用的列数据提取方法，动态识别子列
        完全按照fully_universal_crawler.py的逻辑实现
        """
        phone_data = []
        row_idx = 0

        while row_idx < len(data_rows):
            row = data_rows[row_idx]
            cells = row.find_all(['td', 'th'])

            if col_idx >= len(cells):
                row_idx += 1
                continue

            cell = cells[col_idx]
            cell_text = cell.get_text(strip=True)

            # 跳过空单元格
            if not cell_text or cell_text == '&nbsp;':
                row_idx += 1
                continue

            # 检查是否为子列标题（不以7位或11位数字结尾的文字）
            if self._is_sub_column_title_universal(cell_text):
                # 这是一个子列标题
                sub_column_name = self._extract_sub_column_name_universal(cell_text)
                self.logger.info(f"      发现子列: {sub_column_name}")

                # 提取该子列下的所有电话数据
                sub_data, next_row_idx = self._extract_sub_column_data_universal(
                    data_rows, row_idx + 1, col_idx, base_unit_name, second_level_name, sub_column_name
                )
                phone_data.extend(sub_data)
                row_idx = next_row_idx
            else:
                # 这是电话数据行，使用原始列头作为单位名称
                phone_matches = re.findall(self.phone_pattern, cell_text)

                for phone in phone_matches:
                    user_name = self._extract_user_name_fully_universal(cell_text, phone)

                    # 根据是否有第二层单位名称构建单位名称
                    if second_level_name:
                        unit_name = f"{base_unit_name}-{second_level_name}-{column_header}"
                    else:
                        unit_name = f"{base_unit_name}-{column_header}"

                    phone_data.append({
                        '电话号码': phone,
                        '电话用户名': user_name,
                        '单位名称': unit_name
                    })
                    self.logger.info(f"      找到: {phone} | {user_name} | {unit_name}")

                row_idx += 1

        return phone_data

    def _is_sub_column_title_universal(self, text: str) -> bool:
        """
        判断是否为子列标题
        完全按照fully_universal_crawler.py的逻辑
        """
        # 不以7位或11位数字结尾
        if re.search(r'\d{7}$|\d{11}$', text):
            return False

        # 不包含冒号（冒号通常表示电话号码行）
        if ':' in text or '：' in text:
            return False

        # 包含中文字符
        if not re.search(r'[\u4e00-\u9fa5]', text):
            return False

        # 长度合理（1-20个字符）
        if len(text) < 1 or len(text) > 20:
            return False

        # 不是纯数字
        if text.isdigit():
            return False

        return True

    def _extract_sub_column_name_universal(self, text: str) -> str:
        """
        提取子列名称
        完全按照fully_universal_crawler.py的逻辑
        """
        # 移除HTML标签和多余的空白
        clean_text = re.sub(r'<[^>]+>', '', text).strip()

        # 提取中文字符和数字
        clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()]', '', clean_text)

        return clean_text if clean_text else text

    def _extract_sub_column_data_universal(self, data_rows, start_row_idx: int, col_idx: int,
                                         base_unit_name: str, second_level_name: Optional[str],
                                         sub_column_name: str) -> tuple:
        """
        提取子列下的电话数据
        完全按照fully_universal_crawler.py的逻辑
        """
        phone_data = []
        row_idx = start_row_idx

        while row_idx < len(data_rows):
            row = data_rows[row_idx]
            cells = row.find_all(['td', 'th'])

            if col_idx >= len(cells):
                row_idx += 1
                continue

            cell = cells[col_idx]
            cell_text = cell.get_text(strip=True)

            # 如果遇到空单元格，跳过
            if not cell_text or cell_text == '&nbsp;':
                row_idx += 1
                continue

            # 如果遇到新的子列标题，结束当前子列
            if self._is_sub_column_title_universal(cell_text):
                break

            # 提取电话号码
            phone_matches = re.findall(self.phone_pattern, cell_text)

            for phone in phone_matches:
                user_name = self._extract_user_name_fully_universal(cell_text, phone)

                # 根据是否有第二层单位名称构建单位名称
                if second_level_name:
                    unit_name = f"{base_unit_name}-{second_level_name}-{sub_column_name}"
                else:
                    unit_name = f"{base_unit_name}-{sub_column_name}"

                phone_data.append({
                    '电话号码': phone,
                    '电话用户名': user_name,
                    '单位名称': unit_name
                })
                self.logger.info(f"        找到: {phone} | {user_name} | {unit_name}")

            row_idx += 1

        return phone_data, row_idx

    def _extract_user_name_fully_universal(self, cell_text: str, phone: str) -> str:
        """
        通用用户名提取方法
        完全按照fully_universal_crawler.py的逻辑
        """
        # 清理文本
        cell_text = re.sub(r'\s+', ' ', cell_text.strip())

        # 方法1：查找电话号码前的内容
        phone_pattern = re.escape(phone)
        pattern1 = r'([^:：]+?)[\s]*[:：]\s*' + phone_pattern
        match1 = re.search(pattern1, cell_text)

        if match1:
            user_name = match1.group(1).strip()
            # 保留中文、英文、数字和常用符号
            user_name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()１２３４５６７８９０\-/]', '', user_name)
            if user_name:
                return user_name

        # 方法2：分割后查找
        parts = cell_text.split(phone)
        if len(parts) > 1:
            before_phone = parts[0].strip()
            before_phone = re.sub(r'[:：\s]+$', '', before_phone)
            cleaned_name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()１２３４５６７８９０\-/]', '', before_phone)
            if cleaned_name:
                return cleaned_name

        # 方法3：提取中文内容
        text_without_phone = cell_text.replace(phone, '')
        cleaned_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()１２３４５６７８９０\-/]', '', text_without_phone)
        if cleaned_text:
            return cleaned_text.strip()

        return "未知用户"

    def _extract_row_data_fully_universal(self, row, base_unit_name: str,
                                        second_level_name: Optional[str]) -> List[Dict[str, str]]:
        """
        通用的行数据提取方法
        完全按照fully_universal_crawler.py的逻辑
        """
        phone_data = []
        cells = row.find_all(['td', 'th'])

        if len(cells) < 2:
            return phone_data

        cell_texts = [cell.get_text(strip=True) for cell in cells]
        row_text = ' '.join(cell_texts)

        # 查找电话号码
        phones = re.findall(self.phone_pattern, row_text)

        for phone in phones:
            # 查找对应的姓名
            name = self._find_name_in_cells_universal(cell_texts, phone)

            if name and self._is_valid_phone(phone):
                # 确定单位名称
                unit = second_level_name if second_level_name else base_unit_name

                phone_data.append({
                    '电话号码': phone,
                    '电话用户名': name,
                    '单位名称': unit
                })
                self.logger.info(f"📞 提取到: {phone} - {name} ({unit})")

        return phone_data

    def _find_name_in_cells_universal(self, cell_texts: List[str], phone: str) -> Optional[str]:
        """
        在单元格中查找与电话号码对应的姓名
        完全按照fully_universal_crawler.py的逻辑
        """
        phone_cell_idx = -1

        # 找到包含电话号码的单元格位置
        for idx, cell_text in enumerate(cell_texts):
            if phone in cell_text:
                phone_cell_idx = idx
                break

        # 根据电话号码位置推断姓名位置
        name = ""
        if phone_cell_idx > 0:
            # 电话号码在后面，姓名通常在前一个单元格
            name = cell_texts[phone_cell_idx - 1]
        elif phone_cell_idx == 0 and len(cell_texts) > 1:
            # 电话号码在第一列，姓名可能在第二列
            name = cell_texts[1]

        return self._clean_name_universal(name)

    def _clean_name_universal(self, name: str) -> str:
        """
        清理和验证姓名字符串
        完全按照fully_universal_crawler.py的逻辑
        """
        if not name:
            return ""

        # 移除常见的职务和标识词
        name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长)', '', name)

        # 移除标点符号
        name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)

        # 移除数字
        name = re.sub(r'\d+', '', name)

        # 去除首尾空格
        name = name.strip()

        # 验证姓名格式：2-4个中文字符
        if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
            return name
        return ""
    
    def _extract_table_data_universal(self, table, base_unit_name: str,
                                     second_level_name: Optional[str]) -> List[PhoneRecord]:
        """
        完全通用的表格数据提取方法
        基于fully_universal_crawler.py的完整逻辑，支持复杂的表格结构
        """
        records = []

        # 获取表格的所有行
        rows = table.find_all('tr')
        if not rows:
            return records

        # 分析表格结构，找到表头行
        header_row = None
        data_rows = []

        for i, row in enumerate(rows):
            cells = row.find_all(['td', 'th'])
            if not cells:
                continue

            # 判断是否为表头行（包含"姓名"、"电话"等关键词）
            row_text = ' '.join([cell.get_text(strip=True) for cell in cells])
            if any(keyword in row_text for keyword in ['姓名', '电话', '联系方式', '办公室', '手机']):
                if header_row is None:  # 只取第一个表头行
                    header_row = row
                    continue

            # 其他行作为数据行
            data_rows.append(row)

        if not data_rows:
            self.logger.info("    表格中没有数据行")
            return records

        # 如果有表头，分析列结构
        if header_row:
            header_cells = header_row.find_all(['td', 'th'])
            column_headers = [cell.get_text(strip=True) for cell in header_cells]
            self.logger.info(f"    表头列: {column_headers}")

            # 处理每一列
            for col_idx, column_header in enumerate(column_headers):
                if self._is_phone_column(column_header):
                    self.logger.info(f"    处理电话列: {column_header}")
                    col_records = self._extract_column_data_universal(
                        data_rows, col_idx, base_unit_name, second_level_name, column_header
                    )
                    records.extend(col_records)
        else:
            # 没有表头的情况，逐行处理
            self.logger.info("    没有表头，逐行处理")
            for row in data_rows:
                row_records = self._extract_row_data_universal(
                    row, base_unit_name, second_level_name
                )
                records.extend(row_records)

        return records

    def _is_phone_column(self, column_header: str) -> bool:
        """判断是否为电话列"""
        phone_keywords = ['电话', '联系方式', '办公室', '手机', '座机', '内线']
        return any(keyword in column_header for keyword in phone_keywords)

    def _extract_column_data_universal(self, data_rows, col_idx: int, base_unit_name: str,
                                     second_level_name: Optional[str], column_header: str) -> List[PhoneRecord]:
        """
        完全通用的列数据提取方法，动态识别子列
        基于fully_universal_crawler.py的逻辑
        """
        records = []
        row_idx = 0

        while row_idx < len(data_rows):
            row = data_rows[row_idx]
            cells = row.find_all(['td', 'th'])

            if col_idx >= len(cells):
                row_idx += 1
                continue

            cell = cells[col_idx]
            cell_text = cell.get_text(strip=True)

            # 跳过空单元格
            if not cell_text or cell_text == '&nbsp;':
                row_idx += 1
                continue

            # 检查是否为子列标题（不以7位或11位数字结尾的文字）
            if self._is_sub_column_title(cell_text):
                # 这是一个子列标题
                sub_column_name = self._extract_sub_column_name(cell_text)
                self.logger.info(f"      发现子列: {sub_column_name}")

                # 提取该子列下的所有电话数据
                sub_records, next_row_idx = self._extract_sub_column_phone_data(
                    data_rows, row_idx + 1, col_idx, base_unit_name, second_level_name, sub_column_name
                )
                records.extend(sub_records)
                row_idx = next_row_idx
            else:
                # 这是电话数据行，使用原始列头作为单位名称
                phone_matches = re.findall(self.phone_pattern, cell_text)

                for phone in phone_matches:
                    user_name = self._extract_user_name_universal(cell_text, phone)

                    # 根据是否有第二层单位名称构建单位名称
                    if second_level_name:
                        unit_name = f"{base_unit_name}-{second_level_name}-{column_header}"
                    else:
                        unit_name = f"{base_unit_name}-{column_header}"

                    record = PhoneRecord(
                        phone=phone,
                        unit=unit_name,
                        name=user_name,
                        extraction_method="表格模式"
                    )
                    records.append(record)
                    self.logger.info(f"      找到: {phone} | {user_name} | {unit_name}")

                row_idx += 1

        return records
    
    def _find_name_in_cells(self, cell_texts: List[str], phone: str) -> Optional[str]:
        """在单元格中查找与电话号码对应的姓名"""
        phone_cell_idx = -1
        
        # 找到包含电话号码的单元格位置
        for idx, cell_text in enumerate(cell_texts):
            if phone in cell_text:
                phone_cell_idx = idx
                break
        
        # 根据电话号码位置推断姓名位置
        name = ""
        if phone_cell_idx > 0:
            # 电话号码在后面，姓名通常在前一个单元格
            name = cell_texts[phone_cell_idx - 1]
        elif phone_cell_idx == 0 and len(cell_texts) > 1:
            # 电话号码在第一列，姓名可能在第二列
            name = cell_texts[1]
        
        return self._clean_name(name)
    
    def _extract_from_text(self, html_content: str, unit_name: str) -> List[PhoneRecord]:
        """
        从无表格线的页面提取电话信息
        基于phone_crawler_xzgzb.py的逻辑，特别处理矿领导页面
        """
        self.logger.info("📝 使用文本模式提取电话信息")
        phone_records = []

        # 特殊处理矿领导页面
        if unit_name == "矿领导":
            return self._extract_from_leader_page(html_content, unit_name)

        # 分行处理HTML内容
        lines = html_content.split('\n')

        for line_num, line in enumerate(lines, 1):
            # 清理HTML标签
            clean_line = re.sub(r'<[^>]+>', '', line).strip()

            if not clean_line:
                continue

            # 查找电话号码
            phones = re.findall(self.phone_pattern, clean_line)

            for phone in phones:
                # 在电话号码前查找中文名称
                phone_index = clean_line.find(phone)
                if phone_index > 0:
                    # 获取电话号码前的文本
                    before_phone = clean_line[:phone_index].strip()

                    # 提取完整的中文名称
                    name = self._extract_complete_name(before_phone)

                    if name and len(name) >= 2 and self._is_valid_phone(phone):
                        record = PhoneRecord(
                            phone=phone,
                            unit=unit_name,
                            name=name,
                            extraction_method="文本模式"
                        )

                        # 避免重复记录
                        if not self._is_duplicate_record(phone_records, record):
                            phone_records.append(record)
                            self.logger.info(f"📞 提取到: {phone} - {name}")

        self.logger.info(f"✅ 文本模式提取完成，共 {len(phone_records)} 条记录")
        return phone_records

    def _extract_from_leader_page(self, html_content: str, unit_name: str) -> List[PhoneRecord]:
        """
        专门处理矿领导页面的电话信息提取
        基于phone_scraper_simple_crawl4ai.py的成功逻辑
        """
        self.logger.info("👑 使用矿领导专用模式提取电话信息")

        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        phone_records = []

        # 查找页面中的所有表格
        tables = soup.find_all('table')
        self.logger.info(f"📋 找到 {len(tables)} 个表格")

        # 遍历所有表格，查找包含矿领导电话信息的表格
        for table_idx, table in enumerate(tables):
            table_text = table.get_text()

            # 通过关键词识别目标表格
            if "矿领导" in table_text or "姓名" in table_text or "办公室电话" in table_text:
                self.logger.info(f"🎯 在第{table_idx+1}个表格中找到矿领导表格")

                # 获取表格中的所有行
                rows = table.find_all('tr')

                # 遍历表格的每一行
                for row_idx, row in enumerate(rows):
                    # 获取行中的所有单元格
                    cells = row.find_all(['td', 'th'])

                    # 跳过空行或单元格数量过少的行
                    if len(cells) < 2:
                        continue

                    # 提取每个单元格的文本内容
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    row_text = ' '.join(cell_texts)

                    # 使用正则表达式查找电话号码
                    phones = re.findall(self.phone_pattern, row_text)

                    # 如果找到电话号码，进行进一步处理
                    if phones:
                        self.logger.info(f"🔍 第{row_idx+1}行找到电话: {phones}")

                        # 处理每个找到的电话号码
                        for phone in phones:
                            # 查找与电话号码对应的姓名
                            name = ""
                            phone_cell_idx = -1

                            # 找到包含电话号码的单元格位置
                            for idx, cell_text in enumerate(cell_texts):
                                if phone in cell_text:
                                    phone_cell_idx = idx
                                    break

                            # 根据电话号码位置推断姓名位置
                            if phone_cell_idx > 0:
                                # 电话号码在后面，姓名通常在前一个单元格
                                name = cell_texts[phone_cell_idx - 1]
                            elif phone_cell_idx == 0 and len(cell_texts) > 1:
                                # 电话号码在第一列，姓名可能在第二列
                                name = cell_texts[1]

                            # 清理和验证姓名
                            name = self._clean_leader_name(name)

                            # 验证电话号码和姓名的有效性
                            if self._is_valid_phone(phone) and name:
                                record = PhoneRecord(
                                    phone=phone,
                                    unit=unit_name,
                                    name=name,
                                    extraction_method="矿领导表格模式"
                                )
                                phone_records.append(record)
                                self.logger.info(f"👑 提取到矿领导: {phone} - {name}")

                break  # 找到矿领导表格后退出循环

        self.logger.info(f"✅ 矿领导模式提取完成，共 {len(phone_records)} 条记录")
        return phone_records

    def _clean_leader_name(self, name: str) -> str:
        """
        专门清理矿领导姓名的方法
        基于phone_scraper_simple_crawl4ai.py的成功逻辑
        """
        if not name:
            return ""

        # 移除常见的职务和标识词
        name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长)', '', name)

        # 移除标点符号
        name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)

        # 移除数字
        name = re.sub(r'\d+', '', name)

        # 去除首尾空格
        name = name.strip()

        # 验证姓名格式：2-4个中文字符
        if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
            return name
        return ""
    
    def _extract_complete_name(self, text: str) -> str:
        """
        提取完整的中文名称，保留数字（如"机动能源部办公大楼105"中的"105"）
        修复方法一：需要保留冒号前的数字
        """
        if not text:
            return ""

        # 移除常见的分隔符和标点，但保留数字
        text = re.sub(r'[、，,\(\)（）\[\]【】\s]+', ' ', text)

        # 分割成词组
        words = text.split()

        # 从右到左查找最后一个包含中文的词组，保留其中的数字
        for word in reversed(words):
            # 提取中文字符和数字的组合
            chinese_and_numbers = re.findall(r'[\u4e00-\u9fa5\d]+', word)
            if chinese_and_numbers:
                # 取最长的中文+数字字符串
                longest_match = max(chinese_and_numbers, key=len)
                # 确保包含至少2个中文字符
                if len(re.findall(r'[\u4e00-\u9fa5]', longest_match)) >= 2:
                    return longest_match

        # 如果上面的方法没找到，尝试从整个文本中提取最后的中文+数字组合
        all_matches = re.findall(r'[\u4e00-\u9fa5\d]+', text)
        if all_matches:
            # 返回最后一个包含中文的匹配
            for match in reversed(all_matches):
                if len(re.findall(r'[\u4e00-\u9fa5]', match)) >= 2:
                    return match

        return ""
    
    def _clean_name(self, name: str) -> str:
        """清理和验证姓名字符串"""
        if not name:
            return ""
        
        # 移除常见的职务和标识词
        name = re.sub(r'(姓名|联系人|负责人|主任|经理|厂长|部长|科长)', '', name)
        
        # 移除标点符号
        name = re.sub(r'[：:、，,\(\)（）\[\]【】]', '', name)
        
        # 移除数字
        name = re.sub(r'\d+', '', name)
        
        # 去除首尾空格
        name = name.strip()
        
        # 验证姓名格式：2-4个中文字符
        if 2 <= len(name) <= 4 and re.match(r'^[\u4e00-\u9fff]+$', name):
            return name
        return ""
    
    def _is_valid_phone(self, phone: str) -> bool:
        """验证电话号码格式"""
        # 只接受7位内线电话或11位手机号码
        return len(phone) == 7 or len(phone) == 11
    
    def _is_sub_column_title(self, text: str) -> bool:
        """
        判断是否为子列标题
        基于fully_universal_crawler.py的逻辑
        """
        # 不以7位或11位数字结尾
        if re.search(r'\d{7}$|\d{11}$', text):
            return False

        # 不包含冒号（冒号通常表示电话号码行）
        if ':' in text or '：' in text:
            return False

        # 包含中文字符
        if not re.search(r'[\u4e00-\u9fa5]', text):
            return False

        # 长度合理（1-20个字符）
        if len(text) < 1 or len(text) > 20:
            return False

        # 不是纯数字
        if text.isdigit():
            return False

        return True

    def _extract_sub_column_name(self, text: str) -> str:
        """
        提取子列名称
        基于fully_universal_crawler.py的逻辑
        """
        # 移除HTML标签和多余的空白
        clean_text = re.sub(r'<[^>]+>', '', text).strip()

        # 提取中文字符和数字
        clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()]', '', clean_text)

        return clean_text if clean_text else text

    def _extract_sub_column_phone_data(self, data_rows, start_row_idx: int, col_idx: int,
                                     base_unit_name: str, second_level_name: Optional[str],
                                     sub_column_name: str) -> tuple:
        """
        提取子列下的电话数据
        基于fully_universal_crawler.py的逻辑
        """
        records = []
        row_idx = start_row_idx

        while row_idx < len(data_rows):
            row = data_rows[row_idx]
            cells = row.find_all(['td', 'th'])

            if col_idx >= len(cells):
                row_idx += 1
                continue

            cell = cells[col_idx]
            cell_text = cell.get_text(strip=True)

            # 如果遇到空单元格，跳过
            if not cell_text or cell_text == '&nbsp;':
                row_idx += 1
                continue

            # 如果遇到新的子列标题，结束当前子列
            if self._is_sub_column_title(cell_text):
                break

            # 提取电话号码
            phone_matches = re.findall(self.phone_pattern, cell_text)

            for phone in phone_matches:
                user_name = self._extract_user_name_universal(cell_text, phone)

                # 根据是否有第二层单位名称构建单位名称
                if second_level_name:
                    unit_name = f"{base_unit_name}-{second_level_name}-{sub_column_name}"
                else:
                    unit_name = f"{base_unit_name}-{sub_column_name}"

                record = PhoneRecord(
                    phone=phone,
                    unit=unit_name,
                    name=user_name,
                    extraction_method="表格模式"
                )
                records.append(record)
                self.logger.info(f"        找到: {phone} | {user_name} | {unit_name}")

            row_idx += 1

        return records, row_idx

    def _extract_row_data_universal(self, row, base_unit_name: str,
                                   second_level_name: Optional[str]) -> List[PhoneRecord]:
        """
        通用的行数据提取方法
        """
        records = []
        cells = row.find_all(['td', 'th'])

        if len(cells) < 2:
            return records

        cell_texts = [cell.get_text(strip=True) for cell in cells]
        row_text = ' '.join(cell_texts)

        # 查找电话号码
        phones = re.findall(self.phone_pattern, row_text)

        for phone in phones:
            # 查找对应的姓名
            name = self._find_name_in_cells(cell_texts, phone)

            if name and self._is_valid_phone(phone):
                # 确定单位名称
                unit = second_level_name if second_level_name else base_unit_name

                record = PhoneRecord(
                    phone=phone,
                    unit=unit,
                    name=name,
                    extraction_method="表格模式"
                )
                records.append(record)
                self.logger.info(f"📞 提取到: {phone} - {name} ({unit})")

        return records

    def _extract_user_name_universal(self, cell_text: str, phone: str) -> str:
        """
        通用的用户名提取方法
        基于fully_universal_crawler.py的逻辑
        """
        # 移除电话号码，获取剩余文本
        text_without_phone = cell_text.replace(phone, '').strip()

        # 移除常见的分隔符
        text_without_phone = re.sub(r'[：:、，,\(\)（）\[\]【】\s]+', ' ', text_without_phone)

        # 提取中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fa5]+', text_without_phone)

        if chinese_chars:
            # 取最长的中文字符串作为用户名
            longest_chinese = max(chinese_chars, key=len)
            if len(longest_chinese) >= 2:
                return longest_chinese

        # 如果没有找到合适的中文名称，返回清理后的文本
        clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text_without_phone)
        return clean_text[:10] if clean_text else "未知"

    def _mark_duplicate_phones(self, records: List[PhoneRecord]) -> List[PhoneRecord]:
        """
        标记重复的电话号码
        基于fully_universal_crawler.py的重复号码标记功能
        """
        phone_count = {}

        # 统计每个电话号码的出现次数
        for record in records:
            phone = record.phone
            if phone not in phone_count:
                phone_count[phone] = 0
            phone_count[phone] += 1

        # 标记重复的电话号码
        for record in records:
            if phone_count[record.phone] > 1:
                # 在用户名后添加重复标记
                if "（重复）" not in record.name:
                    record.name = f"{record.name}（重复）"
                self.logger.info(f"🔄 标记重复号码: {record.phone} - {record.name}")

        return records

    def _is_duplicate_record(self, records: List[PhoneRecord], new_record: PhoneRecord) -> bool:
        """检查是否为重复记录"""
        for record in records:
            if (record.phone == new_record.phone and
                record.name == new_record.name and
                record.unit == new_record.unit):
                return True
        return False


# 便捷函数
def extract_phone_info_simple(html_content: str, unit_name: str, 
                             page_type: str = "auto") -> List[Dict[str, str]]:
    """
    简单的电话信息提取函数
    
    Args:
        html_content: HTML内容
        unit_name: 单位名称
        page_type: 页面类型
        
    Returns:
        List[Dict[str, str]]: 电话记录字典列表
    """
    extractor = PhoneExtractor()
    records = extractor.extract_phone_info(html_content, unit_name, page_type)
    
    # 转换为字典格式
    return [
        {
            '电话号码': record.phone,
            '单位名称': record.unit,
            '电话用户名': record.name
        }
        for record in records
    ]


if __name__ == "__main__":
    # 测试代码
    test_html = """
    <table border="1">
        <tr><td>张三</td><td>1234567</td></tr>
        <tr><td>李四</td><td>7654321</td></tr>
    </table>
    """
    
    extractor = PhoneExtractor()
    records = extractor.extract_phone_info(test_html, "测试单位")
    
    for record in records:
        print(f"{record.phone} - {record.name} ({record.unit})")
