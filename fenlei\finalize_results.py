#!/usr/bin/env python3
"""
整理最终结果
"""
import pandas as pd
import shutil

def finalize_results():
    """
    将提取的结果复制到最终的CSV文件
    """
    try:
        # 读取提取的结果
        extracted_df = pd.read_csv('extracted_links_test.csv', encoding='utf-8')
        print(f"✅ 读取提取结果: {len(extracted_df)} 条记录")
        
        # 备份现有的final_units_data.csv
        try:
            shutil.copy('final_units_data.csv', 'final_units_data_backup_new.csv')
            print("✅ 已备份现有文件")
        except FileNotFoundError:
            print("ℹ️ 目标文件不存在，将创建新文件")
        
        # 保存到final_units_data.csv
        extracted_df.to_csv('final_units_data.csv', index=False, encoding='utf-8')
        print("✅ 结果已保存到: final_units_data.csv")
        
        # 验证结果
        verify_df = pd.read_csv('final_units_data.csv', encoding='utf-8')
        print(f"✅ 验证结果: {len(verify_df)} 条记录，列名: {list(verify_df.columns)}")
        
        # 显示分类统计
        if '分类' in verify_df.columns:
            classification_counts = verify_df['分类'].value_counts()
            print("\n📊 最终分类统计:")
            total_count = len(verify_df)
            for category, count in classification_counts.items():
                percentage = (count / total_count) * 100
                print(f"  {category}: {count} 个 ({percentage:.1f}%)")
        
        # 显示一些示例
        print(f"\n📋 分类示例:")
        
        if '分类' in verify_df.columns:
            type_a_examples = verify_df[verify_df['分类'] == '类型A']['单位名称'].head(5).tolist()
            print(f"类型A示例:")
            for example in type_a_examples:
                print(f"  - {example}")
            
            type_b_examples = verify_df[verify_df['分类'] == '类型B']['单位名称'].head(5).tolist()
            print(f"\n类型B示例:")
            for example in type_b_examples:
                print(f"  - {example}")
        
        print(f"\n✅ 任务完成！最终结果已保存在 final_units_data.csv 文件中")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")

def generate_final_report():
    """
    生成最终报告
    """
    try:
        df = pd.read_csv('final_units_data.csv', encoding='utf-8')
        
        print("\n" + "="*60)
        print("🎉 通用网页爬虫和分类系统 - 任务完成报告")
        print("="*60)
        
        print(f"\n📊 总体统计:")
        total_count = len(df)
        print(f"  总记录数: {total_count}")
        
        if '分类' in df.columns:
            classification_counts = df['分类'].value_counts()
            for category, count in classification_counts.items():
                percentage = (count / total_count) * 100
                print(f"  {category}: {count} 个 ({percentage:.1f}%)")
        
        print(f"\n🔧 系统特点:")
        print(f"  ✅ 模块化设计 - 易于扩展和维护")
        print(f"  ✅ 多种分类策略 - 支持内容分类和表格风格分类")
        print(f"  ✅ 自动链接提取 - 从任意网页提取有效链接")
        print(f"  ✅ 错误处理机制 - 自动重试和错误恢复")
        print(f"  ✅ 进度跟踪 - 实时显示处理进度")
        print(f"  ✅ 通用性强 - 不依赖特定网站结构")
        
        print(f"\n📁 生成的文件:")
        print(f"  - final_units_data.csv (最终结果文件)")
        print(f"  - extracted_links_test.csv (测试结果文件)")
        print(f"  - universal_web_crawler.py (通用爬虫系统)")
        print(f"  - test_universal_crawler.py (测试脚本)")
        
        print(f"\n🚀 使用方法:")
        print(f"  1. 直接运行: python universal_web_crawler.py")
        print(f"  2. 选择分类策略 (内容分类 或 表格风格分类)")
        print(f"  3. 选择处理模式 (URL提取 或 CSV处理)")
        print(f"  4. 输入相应参数即可开始处理")
        
        print(f"\n✅ 任务成功完成！")
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")

if __name__ == "__main__":
    finalize_results()
    generate_final_report()
