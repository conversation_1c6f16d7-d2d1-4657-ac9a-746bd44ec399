#!/usr/bin/env python3
"""
智能适应版本：动态识别和提取6个区域的单位名称和子链接
通过内容特征识别表格，适应网页结构变化和单位名称变动
"""

import asyncio
import csv
import re
from crawl4ai import AsyncWebCrawler
from bs4 import BeautifulSoup
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('adaptive_unit_extractor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdaptiveUnitExtractor:
    def __init__(self):
        self.url = "http://172.18.1.16/phone/20201227.html"
        self.base_url = "http://172.18.1.16"
        self.units_data = []

        # 定义区域识别关键词（更灵活的识别方式）
        self.region_keywords = {
            "机关部室办公电话": ["机关部室", "机关", "部室"],
            "主要生产单位办公电话": ["主要生产单位", "生产单位", "主要生产"],
            "主要经营企业办公电话": ["主要经营企业", "经营企业", "主要经营"],
            "辅助生产企业办公电话": ["辅助生产企业", "辅助生产", "辅助企业"],
            "主要服务单位办公电话": ["主要服务单位", "服务单位", "主要服务"],
            "项目经理部办公电话": ["项目经理部", "项目经理", "经理部"]
        }

    async def extract_units(self):
        """智能提取单位信息"""
        logger.info(f"开始智能提取单位信息: {self.url}")

        async with AsyncWebCrawler(verbose=True) as crawler:
            try:
                # 爬取网页
                result = await crawler.arun(url=self.url)

                if not result.success:
                    logger.error(f"爬取失败: {result.error_message}")
                    return []

                logger.info("网页爬取成功，开始智能解析HTML")

                # 解析HTML
                soup = BeautifulSoup(result.html, 'html.parser')

                # 智能识别和提取各个区域的单位
                self._smart_extract_regions(soup)

                logger.info(f"智能提取完成，共获得 {len(self.units_data)} 个单位")
                return self.units_data

            except Exception as e:
                logger.error(f"提取过程出错: {str(e)}")
                return []

    def _smart_extract_regions(self, soup):
        """智能识别和提取各个区域的单位信息"""
        logger.info("开始智能识别区域...")

        # 获取所有表格
        tables = soup.find_all('table')
        logger.info(f"找到 {len(tables)} 个表格")

        # 智能识别每个区域的表格
        for region_name, keywords in self.region_keywords.items():
            logger.info(f"智能识别区域: {region_name}")

            # 方法1: 通过表格前的文本标题识别
            target_table = self._find_table_by_title(soup, keywords)

            # 方法2: 如果方法1失败，通过表格内容识别
            if not target_table:
                target_table = self._find_table_by_content(tables, keywords)

            # 方法3: 如果前两种方法都失败，使用模糊匹配
            if not target_table:
                target_table = self._find_table_by_fuzzy_match(soup, keywords)

            if target_table:
                self._extract_units_from_table(target_table, region_name)
            else:
                logger.warning(f"未能识别到区域: {region_name}")

        # 特殊处理项目经理部（可能不在表格中）
        self._extract_project_manager_unit(soup)

    def _find_table_by_title(self, soup, keywords):
        """通过表格前的标题文本识别表格"""
        for keyword in keywords:
            # 查找包含关键词的文本元素
            title_elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))

            for title_element in title_elements:
                # 查找该文本元素后面的表格
                parent = title_element.parent
                if parent:
                    # 向下查找表格
                    next_table = parent.find_next('table')
                    if next_table:
                        logger.info(f"通过标题'{keyword}'找到表格")
                        return next_table
        return None

    def _find_table_by_content(self, tables, keywords):
        """通过表格内容识别表格"""
        for table in tables:
            table_text = table.get_text()

            # 检查表格是否包含相关关键词
            for keyword in keywords:
                if keyword in table_text:
                    # 进一步验证：检查是否有链接
                    links = table.find_all('a', href=True)
                    if len(links) > 0:
                        logger.info(f"通过内容'{keyword}'找到表格，包含{len(links)}个链接")
                        return table
        return None

    def _find_table_by_fuzzy_match(self, soup, keywords):
        """使用模糊匹配查找表格"""
        tables = soup.find_all('table')

        for table in tables:
            # 获取表格周围的文本内容
            prev_text = ""
            next_text = ""

            # 获取表格前面的文本
            prev_sibling = table.find_previous_sibling()
            if prev_sibling:
                prev_text = prev_sibling.get_text()

            # 获取表格后面的文本
            next_sibling = table.find_next_sibling()
            if next_sibling:
                next_text = next_sibling.get_text()

            # 组合文本进行匹配
            combined_text = prev_text + " " + next_text

            for keyword in keywords:
                if keyword in combined_text:
                    links = table.find_all('a', href=True)
                    if len(links) > 0:
                        logger.info(f"通过模糊匹配'{keyword}'找到表格，包含{len(links)}个链接")
                        return table
        return None

    def _extract_units_from_table(self, table, region_name):
        """从表格中提取单位信息"""
        logger.info(f"从表格中提取 {region_name} 的单位信息")

        # 查找表格中的所有链接
        links = table.find_all('a', href=True)
        logger.info(f"  找到 {len(links)} 个链接")

        region_units = []
        for link in links:
            unit_name = link.get_text(strip=True)
            unit_href = link.get('href')

            if unit_name and unit_href:
                # 构建完整URL
                full_url = self._build_full_url(unit_href)

                unit_data = {
                    '单位名称': unit_name,
                    '子链接': full_url,
                    '区域': region_name
                }

                # 避免重复
                if unit_data not in self.units_data:
                    self.units_data.append(unit_data)
                    region_units.append(unit_name)
                    logger.info(f"    ✓ {unit_name} -> {full_url}")

        logger.info(f"  {region_name} 提取完成，共 {len(region_units)} 个单位")
        return region_units

    def _extract_project_manager_unit(self, soup):
        """智能提取项目经理部单位信息"""
        logger.info("智能提取项目经理部办公电话")

        # 查找包含"项目经理部"的链接
        project_links = soup.find_all('a', href=True)

        for link in project_links:
            link_text = link.get_text(strip=True)
            if '项目经理部' in link_text:
                unit_href = link.get('href')
                full_url = self._build_full_url(unit_href)

                unit_data = {
                    '单位名称': link_text,
                    '子链接': full_url,
                    '区域': '项目经理部办公电话'
                }

                # 避免重复
                if unit_data not in self.units_data:
                    self.units_data.append(unit_data)
                    logger.info(f"    ✓ {link_text} -> {full_url}")
                break

    def _build_full_url(self, href):
        """构建完整URL"""
        if href.startswith('/'):
            return self.base_url + href
        elif href.startswith('http'):
            return href
        else:
            return f"{self.base_url}/phone/{href}"

    def save_data(self):
        """保存数据到CSV文件"""
        if not self.units_data:
            logger.warning("没有数据可保存")
            return

        # 只保存CSV文件 - 只保留单位名称和子链接两列
        csv_file = './final_units_data.csv'
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['单位名称', '子链接']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            # 只写入需要的两列
            for unit in self.units_data:
                writer.writerow({
                    '单位名称': unit['单位名称'],
                    '子链接': unit['子链接']
                })

        logger.info(f"数据已保存到: {csv_file}")

        # 生成统计报告
        self._generate_report()

    def _generate_report(self):
        """生成简化统计报告"""
        logger.info("\n" + "="*60)
        logger.info("📊 单位提取报告")
        logger.info("="*60)

        total_units = len(self.units_data)

        logger.info(f"\n📈 总体统计:")
        logger.info(f"   提取单位总数: {total_units}")
        logger.info(f"   数据格式: CSV文件，包含'单位名称'和'子链接'两列")

        # 按链接路径分类统计
        path_stats = {}
        for unit in self.units_data:
            link = unit['子链接']
            if '机关部室' in link:
                category = "机关部室办公电话"
            elif '主要生产单位' in link:
                category = "主要生产单位办公电话"
            elif '主要经营企业' in link:
                category = "主要经营企业办公电话"
            elif '辅助生产企业' in link:
                category = "辅助生产企业办公电话"
            elif '主要服务单位' in link:
                category = "主要服务单位办公电话"
            elif 'xmjlb' in link:
                category = "项目经理部办公电话"
            else:
                category = "其他"

            if category not in path_stats:
                path_stats[category] = []
            path_stats[category].append(unit['单位名称'])

        logger.info(f"\n📋 分类统计:")
        for category, units in path_stats.items():
            logger.info(f"   ✅ {category}: {len(units)} 个单位")

        logger.info(f"\n� 数据提取完成！")

async def main():
    """主函数"""
    extractor = AdaptiveUnitExtractor()

    # 智能提取单位信息
    units_data = await extractor.extract_units()

    if units_data:
        # 保存数据到CSV文件
        extractor.save_data()
        logger.info("🎉 智能单位提取任务完成！")
        logger.info("💡 数据已保存到 final_units_data.csv")
    else:
        logger.error("❌ 提取失败，没有获得任何数据")

if __name__ == "__main__":
    asyncio.run(main())
