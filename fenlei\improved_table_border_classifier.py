#!/usr/bin/env python3
"""
改进的表格边框样式分类器
基于CSS样式和表格结构进行更精确的分类
"""

import requests
import time
import json
import re
import logging
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
from bs4 import BeautifulSoup
import pandas as pd


@dataclass
class CrawlConfig:
    """爬虫配置类"""
    api_base_url: str = "http://172.18.151.239:11235"
    api_token: str = "123456"
    max_retries: int = 15
    retry_delay: float = 3.0
    request_timeout: int = 30
    rate_limit_delay: float = 1.0


@dataclass
class CrawlResult:
    """爬取结果数据类"""
    url: str
    success: bool
    html: str = ""
    markdown: str = ""
    cleaned_html: str = ""
    error_message: str = ""
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class LinkExtractor:
    """链接提取器"""
    
    @staticmethod
    def extract_links_from_html(html_content: str, base_url: str) -> List[Dict[str, str]]:
        """从HTML中提取链接"""
        soup = BeautifulSoup(html_content, 'html.parser')
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text(strip=True)
            
            # 处理相对链接
            absolute_url = urljoin(base_url, href)
            
            # 过滤掉无效链接
            if LinkExtractor._is_valid_link(absolute_url, text):
                links.append({
                    'text': text,
                    'url': absolute_url,
                    'original_href': href
                })
        
        return links
    
    @staticmethod
    def _is_valid_link(url: str, text: str) -> bool:
        """验证链接是否有效"""
        if not text or len(text.strip()) < 2:
            return False
        
        if url.startswith(('javascript:', 'mailto:', '#')):
            return False
        
        parsed = urlparse(url)
        if not parsed.scheme in ['http', 'https']:
            return False
        
        return True


class WebCrawler:
    """网页爬虫核心类"""
    
    def __init__(self, config: CrawlConfig):
        self.config = config
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.api_token}"
        }
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('WebCrawler')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def crawl_url(self, url: str) -> CrawlResult:
        """爬取单个URL"""
        self.logger.info(f"开始爬取: {url}")
        
        try:
            task_id = self._submit_crawl_task(url)
            if not task_id:
                return CrawlResult(url=url, success=False, error_message="Failed to submit crawl task")
            
            return self._get_crawl_result(task_id, url)
            
        except Exception as e:
            self.logger.error(f"爬取URL时发生错误 {url}: {e}")
            return CrawlResult(url=url, success=False, error_message=str(e))
    
    def _submit_crawl_task(self, url: str) -> Optional[str]:
        """提交爬取任务"""
        crawl_endpoint = f"{self.config.api_base_url}/crawl"
        payload = {"urls": [url]}
        
        try:
            response = requests.post(
                crawl_endpoint, 
                headers=self.headers, 
                json=payload, 
                timeout=self.config.request_timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("task_id")
            else:
                self.logger.error(f"提交任务失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"网络请求错误: {e}")
            return None
    
    def _get_crawl_result(self, task_id: str, url: str) -> CrawlResult:
        """获取爬取结果"""
        result_endpoint = f"{self.config.api_base_url}/task/{task_id}"
        
        for attempt in range(self.config.max_retries):
            try:
                time.sleep(self.config.retry_delay)
                response = requests.get(
                    result_endpoint, 
                    headers=self.headers, 
                    timeout=self.config.request_timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    status = result.get("status", "")
                    
                    if status == "completed":
                        self.logger.info(f"爬取成功: {url}")
                        return self._parse_completed_result(result, url)
                    elif status == "failed":
                        error_msg = result.get("error", "Unknown error")
                        self.logger.error(f"任务失败: {error_msg}")
                        return CrawlResult(url=url, success=False, error_message=error_msg)
                    else:
                        self.logger.info(f"任务进行中... 状态: {status} (尝试 {attempt + 1}/{self.config.max_retries})")
                        continue
                else:
                    self.logger.error(f"获取结果失败: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"获取结果时网络错误: {e}")
        
        return CrawlResult(url=url, success=False, error_message="Task timeout or failed")
    
    def _parse_completed_result(self, result: Dict, url: str) -> CrawlResult:
        """解析完成的爬取结果"""
        results = result.get("results", [])
        if results and len(results) > 0:
            crawl_data = results[0]
            return CrawlResult(
                url=url,
                success=True,
                html=crawl_data.get("html", ""),
                markdown=crawl_data.get("markdown", ""),
                cleaned_html=crawl_data.get("cleaned_html", "")
            )
        else:
            return CrawlResult(url=url, success=False, error_message="No results in completed task")


class ImprovedTableBorderClassifier:
    """改进的表格边框样式分类器"""
    
    def __init__(self):
        self.categories = ["无表格线", "有表格线"]
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ImprovedTableBorderClassifier')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def classify(self, crawl_result: CrawlResult) -> str:
        """基于表格结构和布局进行分类"""
        if not crawl_result.success:
            return "未分类"
        
        soup = BeautifulSoup(crawl_result.html, 'html.parser')
        
        # 分析页面的整体表格布局特征
        layout_features = self._analyze_layout_features(soup)
        
        # 根据布局特征进行分类
        if self._is_complex_table_layout(layout_features):
            classification = "有表格线"
        else:
            classification = "无表格线"
        
        self.logger.info(f"布局分析: {layout_features}, 分类: {classification}")
        return classification
    
    def _analyze_layout_features(self, soup) -> Dict:
        """分析页面的布局特征"""
        features = {
            "total_tables": 0,
            "nested_tables": 0,
            "multi_column_tables": 0,
            "complex_structure_tables": 0,
            "has_table_headers": False,
            "max_columns": 0,
            "max_rows": 0,
            "table_complexity_score": 0
        }
        
        tables = soup.find_all('table')
        features["total_tables"] = len(tables)
        
        for table in tables:
            # 检查是否有嵌套表格
            nested = table.find_all('table')
            if len(nested) > 0:
                features["nested_tables"] += 1
            
            # 检查表格的行列结构
            rows = table.find_all('tr')
            if rows:
                features["max_rows"] = max(features["max_rows"], len(rows))
                
                # 检查第一行的列数
                first_row = rows[0]
                cells = first_row.find_all(['td', 'th'])
                if cells:
                    col_count = len(cells)
                    features["max_columns"] = max(features["max_columns"], col_count)
                    
                    # 多列表格
                    if col_count > 2:
                        features["multi_column_tables"] += 1
                    
                    # 检查是否有表头
                    if first_row.find('th'):
                        features["has_table_headers"] = True
            
            # 检查表格的复杂结构特征
            if self._is_complex_table_structure(table):
                features["complex_structure_tables"] += 1
        
        # 计算复杂度分数
        features["table_complexity_score"] = (
            features["multi_column_tables"] * 2 +
            features["complex_structure_tables"] * 3 +
            features["nested_tables"] * 1 +
            (1 if features["has_table_headers"] else 0)
        )
        
        return features
    
    def _is_complex_table_structure(self, table) -> bool:
        """判断表格是否具有复杂结构"""
        # 检查是否有合并单元格
        if table.find_all(attrs={'colspan': True}) or table.find_all(attrs={'rowspan': True}):
            return True
        
        # 检查表格的行数和列数
        rows = table.find_all('tr')
        if len(rows) > 10:  # 超过10行认为是复杂表格
            return True
        
        # 检查是否有多列布局
        if rows:
            first_row = rows[0]
            cells = first_row.find_all(['td', 'th'])
            if len(cells) > 3:  # 超过3列认为是复杂表格
                return True
        
        # 检查表格的CSS类
        table_classes = table.get('class', [])
        if isinstance(table_classes, list):
            class_str = ' '.join(table_classes).lower()
        else:
            class_str = str(table_classes).lower()
        
        # 某些特定的CSS类可能表示复杂表格
        complex_indicators = ['grid', 'data', 'complex', 'border']
        if any(indicator in class_str for indicator in complex_indicators):
            return True
        
        return False
    
    def _is_complex_table_layout(self, features: Dict) -> bool:
        """根据布局特征判断是否为复杂表格布局"""
        # 基于多个特征进行综合判断
        
        # 1. 如果有多列表格（超过2列），倾向于复杂布局
        if features["multi_column_tables"] > 0:
            return True
        
        # 2. 如果有表头，倾向于复杂布局
        if features["has_table_headers"]:
            return True
        
        # 3. 如果复杂度分数较高，判断为复杂布局
        if features["table_complexity_score"] > 2:
            return True
        
        # 4. 如果最大列数超过3，判断为复杂布局
        if features["max_columns"] > 3:
            return True
        
        # 5. 如果有复杂结构的表格，判断为复杂布局
        if features["complex_structure_tables"] > 0:
            return True
        
        return False
    
    def get_categories(self) -> List[str]:
        """获取所有可能的分类"""
        return self.categories


class ImprovedTableBorderCrawlerSystem:
    """改进的表格边框分类爬虫系统主类"""
    
    def __init__(self, config: CrawlConfig = None):
        self.config = config or CrawlConfig()
        self.crawler = WebCrawler(self.config)
        self.classifier = ImprovedTableBorderClassifier()
        self.link_extractor = LinkExtractor()
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ImprovedTableBorderCrawlerSystem')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def extract_and_classify_links(self, source_url: str, output_file: str) -> None:
        """从源URL提取链接并进行表格布局分类"""
        self.logger.info(f"开始处理源URL: {source_url}")
        
        # 1. 爬取源页面
        source_result = self.crawler.crawl_url(source_url)
        if not source_result.success:
            self.logger.error(f"无法爬取源页面: {source_result.error_message}")
            return
        
        # 2. 提取链接
        links = self.link_extractor.extract_links_from_html(source_result.html, source_url)
        self.logger.info(f"从源页面提取到 {len(links)} 个链接")
        
        if not links:
            self.logger.warning("未找到任何有效链接")
            return
        
        # 3. 爬取和分类每个链接
        results = []
        for i, link in enumerate(links, 1):
            self.logger.info(f"处理第 {i}/{len(links)} 个链接: {link['text']}")
            
            # 爬取链接
            crawl_result = self.crawler.crawl_url(link['url'])
            
            # 分类
            if crawl_result.success:
                classification = self.classifier.classify(crawl_result)
                self.logger.info(f"分类结果: {classification}")
            else:
                classification = "爬取失败"
                self.logger.error(f"爬取失败: {crawl_result.error_message}")
            
            results.append({
                '单位名称': link['text'],
                '子链接': link['url'],
                '分类': classification
            })
            
            # 添加延迟避免过于频繁的请求
            time.sleep(self.config.rate_limit_delay)
        
        # 4. 保存结果
        self._save_results(results, output_file)
        self._print_summary(results)
    
    def _save_results(self, results: List[Dict], output_file: str) -> None:
        """保存结果到CSV文件"""
        try:
            df = pd.DataFrame(results)
            df.to_csv(output_file, index=False, encoding='utf-8')
            self.logger.info(f"结果已保存到: {output_file}")
        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
    
    def _print_summary(self, results: List[Dict]) -> None:
        """打印处理摘要"""
        total = len(results)
        categories = {}
        
        for result in results:
            category = result['分类']
            categories[category] = categories.get(category, 0) + 1
        
        self.logger.info(f"\n📊 改进的表格布局分类摘要:")
        self.logger.info(f"总记录数: {total}")
        for category, count in categories.items():
            percentage = (count / total) * 100 if total > 0 else 0
            self.logger.info(f"  {category}: {count} 个 ({percentage:.1f}%)")


def main():
    """主函数"""
    print("🚀 改进的表格边框样式分类爬虫系统")
    print("=" * 50)
    print("基于表格结构和布局特征进行分类：")
    print("  - 第一类：无表格线（简单列表布局）")
    print("  - 第二类：有表格线（复杂表格布局）")
    print()
    
    # 配置系统
    config = CrawlConfig()
    system = ImprovedTableBorderCrawlerSystem(config)
    
    # 使用默认参数
    source_url = "http://172.18.1.16/phone/20201227.html"
    output_file = "final_units_data.csv"
    
    print(f"源URL: {source_url}")
    print(f"输出文件: {output_file}")
    print()
    
    # 开始处理
    try:
        system.extract_and_classify_links(source_url, output_file)
        print("\n✅ 处理完成！")
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")


if __name__ == "__main__":
    main()
