#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严格按照fully_universal_crawler.py逻辑的测试程序
验证方法二的修复效果
"""

import asyncio
import csv
from datetime import datetime
from base_crawler import BaseCrawler, CrawlConfig
from phone_extractor import PhoneExtractor
from csv_processor import CSVProcessor


async def test_strict_fully_universal():
    """严格按照fully_universal_crawler.py逻辑测试采矿场"""
    print("🎯 严格按照fully_universal_crawler.py逻辑测试")
    print("=" * 60)
    
    # 直接测试采矿场（fully_universal_crawler.py验证过的成功案例）
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    
    test_url = "http://172.18.1.16/phone/主要生产单位/采矿场/ckc.html"
    print(f"🌐 测试URL: {test_url}")
    
    html_content = await crawler.crawl_url(test_url)
    if not html_content:
        print("❌ 页面爬取失败")
        return []
    
    print(f"✅ 页面爬取成功，内容长度: {len(html_content)}")
    
    # 使用严格修复后的提取器
    records = extractor.extract_phone_info(html_content, "采矿场", "有表格线")
    
    print(f"✅ 采矿场提取到 {len(records)} 条电话记录")
    
    # 详细检查数据质量
    print(f"\n📊 详细数据质量检查:")
    
    # 统计各种问题
    phone_in_name_count = 0
    date_in_unit_count = 0
    unknown_user_count = 0
    valid_count = 0
    
    for i, record in enumerate(records, 1):
        # 检查用户名是否包含电话号码
        name_has_phone = bool(re.search(r'\d{7,11}', record.name))
        
        # 检查单位名称是否包含日期
        unit_has_date = any(keyword in record.unit for keyword in ['年', '月', '日', '星期'])
        
        # 检查是否为未知用户
        is_unknown_user = record.name == "未知用户"
        
        if name_has_phone:
            phone_in_name_count += 1
            if i <= 10:  # 只显示前10个错误
                print(f"  ❌ {i}. 用户名包含电话号码: {record.name} | {record.phone} | {record.unit}")
        elif unit_has_date:
            date_in_unit_count += 1
            if i <= 10:
                print(f"  ❌ {i}. 单位名称包含日期: {record.name} | {record.phone} | {record.unit}")
        elif is_unknown_user:
            unknown_user_count += 1
            if i <= 10:
                print(f"  ⚠️ {i}. 未知用户: {record.name} | {record.phone} | {record.unit}")
        else:
            valid_count += 1
            if i <= 20:  # 显示前20条正确的
                print(f"  ✅ {i}. {record.phone} - {record.name} ({record.unit})")
    
    print(f"\n📈 质量统计:")
    print(f"  总记录数: {len(records)} 条")
    print(f"  正确记录: {valid_count} 条")
    print(f"  用户名包含电话号码: {phone_in_name_count} 条")
    print(f"  单位名称包含日期: {date_in_unit_count} 条")
    print(f"  未知用户: {unknown_user_count} 条")
    
    error_count = phone_in_name_count + date_in_unit_count
    if len(records) > 0:
        print(f"  数据质量: {valid_count}/{len(records)} 条正确 ({valid_count/len(records)*100:.1f}%)")
        print(f"  错误率: {error_count}/{len(records)} 条错误 ({error_count/len(records)*100:.1f}%)")
        print(f"  未知用户率: {unknown_user_count}/{len(records)} 条 ({unknown_user_count/len(records)*100:.1f}%)")
    
    return records


async def test_multiple_table_units():
    """测试多个有表格线的单位"""
    print(f"\n🎯 测试多个有表格线单位")
    print("=" * 60)
    
    # 加载CSV数据
    csv_processor = CSVProcessor()
    if not csv_processor.load_data():
        print("❌ CSV数据加载失败")
        return []
    
    # 选择几个有表格线的单位进行测试
    test_units = ["采矿场", "选矿厂", "机修厂"]
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    all_records = []
    
    for i, unit_name in enumerate(test_units, 1):
        print(f"\n📋 测试第 {i}/{len(test_units)} 个单位: {unit_name}")
        
        # 获取单位记录
        record = csv_processor.get_unit_record(unit_name)
        if not record:
            print(f"❌ 未找到单位: {unit_name}")
            continue
        
        if record.classification != "有表格线":
            print(f"⚠️ 单位分类不是有表格线: {record.classification}")
            continue
        
        print(f"  🔗 链接: {record.sub_link}")
        
        try:
            # 爬取页面
            html_content = await crawler.crawl_url(record.sub_link)
            if not html_content:
                print(f"❌ 爬取失败: {unit_name}")
                continue
            
            print(f"  ✅ 页面爬取成功，内容长度: {len(html_content)}")
            
            # 使用严格修复后的提取器
            phone_records = extractor.extract_phone_info(
                html_content,
                record.unit_name,
                record.classification
            )
            
            if phone_records:
                all_records.extend(phone_records)
                print(f"  ✅ 提取到 {len(phone_records)} 条电话记录")
                
                # 快速质量检查
                error_count = 0
                unknown_count = 0
                for phone_record in phone_records:
                    name_has_phone = bool(re.search(r'\d{7,11}', phone_record.name))
                    unit_has_date = any(keyword in phone_record.unit for keyword in ['年', '月', '日', '星期'])
                    is_unknown = phone_record.name == "未知用户"
                    
                    if name_has_phone or unit_has_date:
                        error_count += 1
                    if is_unknown:
                        unknown_count += 1
                
                valid_count = len(phone_records) - error_count
                print(f"    质量: {valid_count}/{len(phone_records)} 条正确 ({valid_count/len(phone_records)*100:.1f}%)")
                print(f"    未知用户: {unknown_count} 条 ({unknown_count/len(phone_records)*100:.1f}%)")
            else:
                print(f"  ⚠️ 未提取到电话信息")
            
            # 添加延迟
            await asyncio.sleep(2)
            
        except Exception as e:
            print(f"❌ 处理异常: {e}")
    
    return all_records


def save_strict_test_results(records):
    """保存严格测试结果"""
    print(f"\n🎯 保存严格测试结果")
    print("=" * 60)
    
    if not records:
        print("⚠️ 没有数据可保存")
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存CSV文件
    csv_filename = f"严格fully_universal测试结果_{timestamp}.csv"
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['电话号码', '单位名称', '电话用户名', '提取方法']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in records:
                writer.writerow({
                    '电话号码': record.phone,
                    '单位名称': record.unit,
                    '电话用户名': record.name,
                    '提取方法': record.extraction_method
                })
        
        print(f"✅ CSV文件保存成功: {csv_filename}")
    except Exception as e:
        print(f"❌ CSV文件保存失败: {e}")
    
    # 显示最终统计信息
    print(f"\n📊 严格测试最终统计:")
    print(f"  总电话数: {len(records)} 条")
    
    # 质量统计
    import re
    phone_in_name_count = 0
    date_in_unit_count = 0
    unknown_user_count = 0
    
    for record in records:
        name_has_phone = bool(re.search(r'\d{7,11}', record.name))
        unit_has_date = any(keyword in record.unit for keyword in ['年', '月', '日', '星期'])
        is_unknown_user = record.name == "未知用户"
        
        if name_has_phone:
            phone_in_name_count += 1
        if unit_has_date:
            date_in_unit_count += 1
        if is_unknown_user:
            unknown_user_count += 1
    
    error_count = phone_in_name_count + date_in_unit_count
    valid_count = len(records) - error_count
    
    print(f"  正确记录: {valid_count} 条 ({valid_count/len(records)*100:.1f}%)")
    print(f"  错误记录: {error_count} 条 ({error_count/len(records)*100:.1f}%)")
    print(f"    - 用户名包含电话号码: {phone_in_name_count} 条")
    print(f"    - 单位名称包含日期: {date_in_unit_count} 条")
    print(f"  未知用户: {unknown_user_count} 条 ({unknown_user_count/len(records)*100:.1f}%)")


async def main():
    """主测试函数"""
    print("🕷️ 严格按照fully_universal_crawler.py逻辑的测试程序")
    print("验证方法二的修复效果")
    print("=" * 70)
    
    try:
        # 测试1: 严格按照fully_universal_crawler.py逻辑测试采矿场
        single_records = await test_strict_fully_universal()
        
        # 测试2: 测试多个有表格线单位
        multiple_records = await test_multiple_table_units()
        
        # 合并结果
        all_records = single_records + multiple_records
        
        # 保存测试结果
        save_strict_test_results(all_records)
        
        print(f"\n🎉 严格测试完成！")
        print("=" * 70)
        print("✅ 测试结果:")
        print(f"  - 采矿场单独测试: {len(single_records)} 条记录")
        print(f"  - 多单位测试: {len(multiple_records)} 条记录")
        print(f"  - 总计: {len(all_records)} 条记录")
        
        if all_records:
            import re
            error_count = sum(1 for r in all_records 
                            if bool(re.search(r'\d{7,11}', r.name)) or 
                               any(keyword in r.unit for keyword in ['年', '月', '日', '星期']))
            valid_count = len(all_records) - error_count
            unknown_count = sum(1 for r in all_records if r.name == "未知用户")
            
            print("✅ 修复验证:")
            print(f"  - 数据质量: {valid_count}/{len(all_records)} 条正确 ({valid_count/len(all_records)*100:.1f}%)")
            print(f"  - 错误率: {error_count}/{len(all_records)} 条错误 ({error_count/len(all_records)*100:.1f}%)")
            print(f"  - 未知用户率: {unknown_count}/{len(all_records)} 条 ({unknown_count/len(all_records)*100:.1f}%)")
            print(f"  - fully_universal_crawler.py逻辑集成: {'✅ 成功' if error_count < len(all_records) * 0.1 else '❌ 仍有问题'}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    import re
    asyncio.run(main())
