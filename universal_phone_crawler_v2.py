#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用电话信息爬虫程序 - 方法二
基于fully_universal_crawler.py的深入理解，创建模块化、通用、易读的解决方案
支持爬取所有有表格线的单位电话信息

核心设计原则：
1. 模块化：每个功能独立成模块，便于维护和扩展
2. 通用性：支持所有有表格线的单位，不依赖硬编码
3. 易读性：清晰的代码结构和详细的注释
4. 可测试性：内置数据验证和测试功能
"""

import requests
import pandas as pd
import re
import logging
from bs4 import BeautifulSoup
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import os


class UniversalPhoneCrawler:
    """通用电话信息爬虫类"""
    
    def __init__(self, log_level=logging.INFO):
        """初始化爬虫"""
        self.setup_logging(log_level)
        self.phone_counts = {}  # 用于重复电话号码标记
        
    def setup_logging(self, level):
        """设置日志"""
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(f'crawler_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_unit_url(self, unit_name: str, csv_file: str = 'final_units_data.csv') -> Optional[str]:
        """
        从CSV文件中获取指定单位的URL
        
        Args:
            unit_name: 单位名称（第一层）
            csv_file: CSV文件路径
            
        Returns:
            单位的子链接URL，如果未找到返回None
        """
        try:
            df = pd.read_csv(csv_file, encoding='utf-8')
            target_row = df[df['单位名称'] == unit_name]
            
            if not target_row.empty:
                url = target_row.iloc[0]['子链接']
                self.logger.info(f"✅ 找到单位 '{unit_name}' 的URL: {url}")
                return url
            else:
                self.logger.error(f"❌ 未找到单位 '{unit_name}' 的记录")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ 读取CSV文件时出错: {e}")
            return None
    
    def fetch_webpage(self, url: str) -> Optional[str]:
        """
        获取网页内容
        
        Args:
            url: 网页URL
            
        Returns:
            网页HTML内容，如果失败返回None
        """
        try:
            self.logger.info(f"🌐 正在爬取: {url}")
            response = requests.get(url, timeout=10)
            
            # 智能编码检测
            for encoding in ['gb2312', 'gbk', 'utf-8']:
                response.encoding = encoding
                if '�' not in response.text:
                    break
            
            self.logger.info(f"✅ 爬取成功，内容长度: {len(response.text)} 字符")
            return response.text
            
        except Exception as e:
            self.logger.error(f"❌ 爬取失败: {e}")
            return None
    
    def extract_phone_data(self, html_content: str, base_unit_name: str) -> List[Dict[str, str]]:
        """
        提取电话数据的主入口方法
        
        Args:
            html_content: HTML内容
            base_unit_name: 基础单位名称（第一层）
            
        Returns:
            电话数据列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        phone_data = []
        
        # 查找所有带边框的表格
        tables = soup.find_all('table', {'border': '1'})
        self.logger.info(f"📋 发现 {len(tables)} 个带边框表格")
        
        if not tables:
            self.logger.warning("⚠️ 未找到带边框的表格")
            return []
        
        # 用于存储已找到的第二层单位名称，供后续表格复用
        last_found_second_level = None
        
        for table_idx, table in enumerate(tables):
            self.logger.info(f"📊 处理表格 {table_idx}:")
            
            # 动态获取表格上方的第二层单位名称
            second_level_name = self._extract_second_level_name(table, soup)
            
            if second_level_name:
                self.logger.info(f"  ✅ 找到第二层单位名称: {second_level_name}")
                last_found_second_level = second_level_name
            else:
                # 如果当前表格没有找到，使用最近找到的第二层单位名称
                if last_found_second_level:
                    second_level_name = last_found_second_level
                    self.logger.info(f"  🔄 使用最近的第二层单位名称: {second_level_name}")
                else:
                    self.logger.info(f"  ⚠️ 未找到任何第二层单位名称，将使用两层结构")
            
            # 检查表格是否包含电话信息
            if not self._has_phone_data(table):
                self.logger.info(f"  ⏭️ 跳过：表格不包含电话信息")
                continue
            
            # 提取表格数据
            table_data = self._extract_table_data(table, base_unit_name, second_level_name)
            phone_data.extend(table_data)
        
        # 为重复的电话号码添加星号标记
        phone_data = self._mark_duplicate_phones(phone_data)
        
        self.logger.info(f"✅ 提取完成，共 {len(phone_data)} 条记录")
        return phone_data
    
    def _extract_second_level_name(self, table, soup) -> Optional[str]:
        """
        动态获取表格上方的第二层单位名称
        
        Args:
            table: 表格元素
            soup: BeautifulSoup对象
            
        Returns:
            第二层单位名称，如果未找到返回None
        """
        # 方法1：查找表格前面紧挨着的文本元素
        current = table.previous_sibling
        search_count = 0
        
        while current and search_count < 10:
            if hasattr(current, 'get_text'):
                text = current.get_text(strip=True)
                
                # 检查是否为4个中文字以内的有效单位名称
                if text and len(text) <= 8:  # 4个中文字约8个字符
                    # 检查是否包含中文且不包含电话号码和冒号
                    if (re.search(r'[\u4e00-\u9fa5]', text) and
                        not re.search(r'\d{7,11}', text) and
                        ':' not in text and '：' not in text):
                        # 过滤掉导航文本和无关文本
                        if not any(keyword in text for keyword in ['网站首页', '网上服务', '当前位置', '电话号码']):
                            self.logger.debug(f"    🎯 找到第二层单位名称: {text}")
                            return text
            
            current = current.previous_sibling
            search_count += 1
        
        # 方法2：全局搜索页面中的关键词
        page_text = soup.get_text()
        patterns = [
            r'([\u4e00-\u9fa5]{2,4}机关)',
            r'([\u4e00-\u9fa5]{2,4}现场)',
            r'(基层单位)',
            r'(生产现场)',
            r'(场机关)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                candidate = matches[0]
                self.logger.debug(f"    🔍 从全局搜索找到第二层单位名称: {candidate}")
                return candidate
        
        return None
    
    def _has_phone_data(self, table) -> bool:
        """
        检查表格是否包含电话数据
        
        Args:
            table: 表格元素
            
        Returns:
            如果包含电话数据返回True，否则返回False
        """
        table_text = table.get_text()
        return bool(re.search(r'\b(\d{7}|\d{11})\b', table_text))
    
    def _extract_table_data(self, table, base_unit_name: str, second_level_name: Optional[str]) -> List[Dict[str, str]]:
        """
        从表格中提取电话数据
        
        Args:
            table: 表格元素
            base_unit_name: 基础单位名称（第一层）
            second_level_name: 第二层单位名称
            
        Returns:
            电话数据列表
        """
        phone_data = []
        
        # 获取表格的所有行
        rows = table.find_all('tr')
        if len(rows) < 2:  # 至少要有表头和数据行
            return phone_data
        
        # 获取表头（主列）
        header_row = rows[0]
        header_cells = header_row.find_all(['td', 'th'])
        column_headers = [cell.get_text(strip=True) for cell in header_cells]
        
        self.logger.info(f"    📋 识别到列头: {column_headers}")
        
        # 获取数据行
        data_rows = rows[1:]
        
        # 按列处理数据
        for col_idx, column_header in enumerate(column_headers):
            if not column_header:
                continue
            
            self.logger.info(f"    📊 处理列 {col_idx}: {column_header}")
            
            # 提取该列的所有数据
            col_data = self._extract_column_data(data_rows, col_idx, base_unit_name, second_level_name, column_header)
            phone_data.extend(col_data)
        
        return phone_data

    def _extract_column_data(self, data_rows, col_idx: int, base_unit_name: str,
                           second_level_name: Optional[str], column_header: str) -> List[Dict[str, str]]:
        """
        提取列数据的核心方法

        Args:
            data_rows: 数据行列表
            col_idx: 列索引
            base_unit_name: 基础单位名称（第一层）
            second_level_name: 第二层单位名称
            column_header: 列头名称

        Returns:
            该列的电话数据列表
        """
        phone_data = []
        row_idx = 0

        while row_idx < len(data_rows):
            row = data_rows[row_idx]
            cells = row.find_all(['td', 'th'])

            # 检查列索引是否有效
            if col_idx >= len(cells):
                row_idx += 1
                continue

            cell = cells[col_idx]
            cell_text = cell.get_text(strip=True)

            # 跳过空单元格
            if not cell_text or cell_text == '&nbsp;':
                row_idx += 1
                continue

            # 检查是否为子列标题（不以7位或11位数字结尾的文字）
            if self._is_sub_column_title(cell_text):
                # 这是一个子列标题
                sub_column_name = self._extract_sub_column_name(cell_text)
                self.logger.info(f"      🏷️ 发现子列: {sub_column_name}")

                # 提取该子列下的所有电话数据
                sub_data, next_row_idx = self._extract_sub_column_data(
                    data_rows, row_idx + 1, col_idx, base_unit_name, second_level_name, sub_column_name
                )
                phone_data.extend(sub_data)
                row_idx = next_row_idx
            else:
                # 这是电话数据行，使用原始列头作为单位名称
                phone_matches = re.findall(r'\b(\d{7}|\d{11})\b', cell_text)

                for phone in phone_matches:
                    user_name = self._extract_user_name(cell_text, phone)

                    # 根据是否有第二层单位名称构建单位名称
                    if second_level_name:
                        unit_name = f"{base_unit_name}-{second_level_name}-{column_header}"
                    else:
                        unit_name = f"{base_unit_name}-{column_header}"

                    phone_data.append({
                        '电话号码': phone,
                        '电话用户名': user_name,
                        '单位名称': unit_name
                    })

                    self.logger.info(f"      📞 找到: {phone} | {user_name} | {unit_name}")

                row_idx += 1

        return phone_data

    def _is_sub_column_title(self, text: str) -> bool:
        """
        判断是否为子列标题

        Args:
            text: 文本内容

        Returns:
            如果是子列标题返回True，否则返回False
        """
        # 检查文本最后几位是否为7位或11位数字
        if re.search(r'\d{7}$|\d{11}$', text):
            return False

        # 检查是否包含中文字符
        if not re.search(r'[\u4e00-\u9fa5]', text):
            return False

        # 特殊情况：书记室单独处理
        if text.strip() == '书记室：':
            return False

        return True

    def _extract_sub_column_name(self, text: str) -> str:
        """
        提取子列名称

        Args:
            text: 包含子列名称的文本

        Returns:
            清理后的子列名称
        """
        # 移除末尾的冒号和空格
        name = text.strip().rstrip('：:')
        return name

    def _extract_sub_column_data(self, data_rows, start_row_idx: int, col_idx: int,
                                base_unit_name: str, second_level_name: Optional[str],
                                sub_column_name: str) -> Tuple[List[Dict[str, str]], int]:
        """
        提取子列下的电话数据

        Args:
            data_rows: 数据行列表
            start_row_idx: 开始行索引
            col_idx: 列索引
            base_unit_name: 基础单位名称（第一层）
            second_level_name: 第二层单位名称
            sub_column_name: 子列名称（第三层）

        Returns:
            (电话数据列表, 下一个处理行索引)
        """
        phone_data = []
        row_idx = start_row_idx

        while row_idx < len(data_rows):
            row = data_rows[row_idx]
            cells = row.find_all(['td', 'th'])

            # 检查列索引是否有效
            if col_idx >= len(cells):
                row_idx += 1
                continue

            cell = cells[col_idx]
            cell_text = cell.get_text(strip=True)

            # 跳过空单元格
            if not cell_text or cell_text == '&nbsp;':
                row_idx += 1
                continue

            # 检查子列是否结束
            if self._is_sub_column_end(cell_text, row_idx, data_rows, col_idx):
                break

            # 提取电话数据
            phone_matches = re.findall(r'\b(\d{7}|\d{11})\b', cell_text)

            for phone in phone_matches:
                user_name = self._extract_user_name(cell_text, phone)

                # 构建完整的单位名称
                if second_level_name:
                    unit_name = f"{base_unit_name}-{second_level_name}-{sub_column_name}"
                else:
                    unit_name = f"{base_unit_name}-{sub_column_name}"

                phone_data.append({
                    '电话号码': phone,
                    '电话用户名': user_name,
                    '单位名称': unit_name
                })

                self.logger.info(f"        📞 子列数据: {phone} | {user_name} | {unit_name}")

            row_idx += 1

        return phone_data, row_idx

    def _is_sub_column_end(self, cell_text: str, row_idx: int, data_rows, col_idx: int) -> bool:
        """
        判断子列是否结束

        Args:
            cell_text: 当前单元格文本
            row_idx: 当前行索引
            data_rows: 数据行列表
            col_idx: 列索引

        Returns:
            如果子列结束返回True，否则返回False
        """
        # 检查是否为新的子列标题（不以7位或11位数字结尾且不是"书记室："）
        if (not re.search(r'\d{7}$|\d{11}$', cell_text) and
            cell_text.strip() != '书记室：' and
            re.search(r'[\u4e00-\u9fa5]', cell_text)):
            return True

        # 检查下一行是否没有表格或为空
        if row_idx + 1 < len(data_rows):
            next_row = data_rows[row_idx + 1]
            next_cells = next_row.find_all(['td', 'th'])

            # 如果下一行没有对应的列，说明主列结束
            if col_idx >= len(next_cells):
                return True

            next_cell = next_cells[col_idx]
            next_text = next_cell.get_text(strip=True)

            # 如果下一行为空，继续检查后续行
            if not next_text or next_text == '&nbsp;':
                return False

        return False

    def _extract_user_name(self, cell_text: str, phone: str) -> str:
        """
        从单元格文本中提取用户名

        Args:
            cell_text: 单元格文本
            phone: 电话号码

        Returns:
            用户名
        """
        # 查找电话号码前的文本作为用户名
        pattern = rf'(.+?)[:：]\s*{re.escape(phone)}'
        match = re.search(pattern, cell_text)

        if match:
            user_name = match.group(1).strip()
            # 清理用户名中的特殊字符
            user_name = re.sub(r'[^\u4e00-\u9fa5\w\s\(\)（）]', '', user_name)
            return user_name

        # 如果没有找到冒号分隔，尝试其他方法
        # 移除电话号码，剩下的作为用户名
        user_name = cell_text.replace(phone, '').strip()
        user_name = re.sub(r'[：:]', '', user_name)
        user_name = re.sub(r'[^\u4e00-\u9fa5\w\s\(\)（）]', '', user_name)

        return user_name if user_name else "未知"

    def _mark_duplicate_phones(self, phone_data: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        为重复的电话号码添加星号标记

        Args:
            phone_data: 电话数据列表

        Returns:
            标记后的电话数据列表
        """
        if not phone_data:
            return phone_data

        # 统计电话号码出现次数
        phone_counts = {}
        for record in phone_data:
            phone = record['电话号码']
            phone_counts[phone] = phone_counts.get(phone, 0) + 1

        # 找出重复的电话号码
        duplicate_phones = [phone for phone, count in phone_counts.items() if count > 1]

        if duplicate_phones:
            self.logger.info(f"🔄 发现重复电话号码，添加星号标记:")
            for phone in duplicate_phones:
                self.logger.info(f"  📞 {phone}: {phone_counts[phone]}次重复")

        # 为重复的电话号码添加星号标记
        phone_occurrence = {}
        for record in phone_data:
            phone = record['电话号码']
            if phone in duplicate_phones:
                occurrence = phone_occurrence.get(phone, 0)
                if occurrence > 0:
                    # 第一次重复加一个"*"，第二次重复加两个"*"，以此类推
                    stars = '*' * occurrence
                    record['电话号码'] = f"{phone}{stars}"
                phone_occurrence[phone] = occurrence + 1

        return phone_data

    def crawl_unit_phones(self, unit_name: str, csv_file: str = 'final_units_data.csv') -> List[Dict[str, str]]:
        """
        爬取指定单位的电话信息

        Args:
            unit_name: 单位名称（第一层）
            csv_file: CSV文件路径

        Returns:
            电话数据列表
        """
        self.logger.info(f"🚀 开始爬取单位 '{unit_name}' 的电话信息")

        # 获取单位URL
        url = self.get_unit_url(unit_name, csv_file)
        if not url:
            return []

        # 获取网页内容
        html_content = self.fetch_webpage(url)
        if not html_content:
            return []

        # 提取电话数据
        phone_data = self.extract_phone_data(html_content, unit_name)

        self.logger.info(f"🎉 爬取完成！共获取 {len(phone_data)} 条电话记录")
        return phone_data

    def save_to_csv(self, phone_data: List[Dict[str, str]], filename: str = None) -> str:
        """
        将电话数据保存到CSV文件

        Args:
            phone_data: 电话数据列表
            filename: 文件名，如果为None则自动生成

        Returns:
            保存的文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"phone_data_{timestamp}.csv"

        try:
            df = pd.DataFrame(phone_data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            self.logger.info(f"💾 数据已保存到: {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"❌ 保存文件失败: {e}")
            return ""

    def validate_data(self, phone_data: List[Dict[str, str]]) -> Dict[str, any]:
        """
        验证电话数据的质量

        Args:
            phone_data: 电话数据列表

        Returns:
            验证结果统计
        """
        if not phone_data:
            return {"total": 0, "valid_phones": 0, "invalid_phones": 0, "empty_users": 0}

        total = len(phone_data)
        valid_phones = 0
        invalid_phones = 0
        empty_users = 0

        for record in phone_data:
            phone = record.get('电话号码', '')
            user = record.get('电话用户名', '')

            # 验证电话号码格式
            if re.match(r'^\d{7}(\**)$|^\d{11}(\**)$', phone):
                valid_phones += 1
            else:
                invalid_phones += 1

            # 检查用户名是否为空
            if not user or user == "未知":
                empty_users += 1

        result = {
            "total": total,
            "valid_phones": valid_phones,
            "invalid_phones": invalid_phones,
            "empty_users": empty_users,
            "phone_accuracy": valid_phones / total if total > 0 else 0,
            "user_completeness": (total - empty_users) / total if total > 0 else 0
        }

        self.logger.info(f"📊 数据质量验证结果:")
        self.logger.info(f"  总记录数: {result['total']}")
        self.logger.info(f"  有效电话: {result['valid_phones']} ({result['phone_accuracy']:.1%})")
        self.logger.info(f"  无效电话: {result['invalid_phones']}")
        self.logger.info(f"  空用户名: {result['empty_users']}")
        self.logger.info(f"  用户名完整度: {result['user_completeness']:.1%}")

        return result


def test_crawler_with_standard():
    """
    测试爬虫与标准程序的对比
    """
    print("🧪 开始测试通用电话爬虫...")

    # 创建爬虫实例
    crawler = UniversalPhoneCrawler(log_level=logging.INFO)

    # 测试采矿场数据
    unit_name = "采矿场"
    phone_data = crawler.crawl_unit_phones(unit_name)

    if phone_data:
        # 保存数据
        filename = crawler.save_to_csv(phone_data, f"{unit_name}_phone_data_v2.csv")

        # 验证数据质量
        validation_result = crawler.validate_data(phone_data)

        # 与标准程序对比
        try:
            # 运行标准程序获取对比数据
            import subprocess
            result = subprocess.run(['python', 'fully_universal_crawler.py'],
                                  capture_output=True, text=True, cwd='.')

            if result.returncode == 0:
                print("✅ 标准程序运行成功")

                # 读取标准程序的输出文件
                standard_files = [f for f in os.listdir('.') if f.startswith('采矿场_phone_data_') and f.endswith('.csv')]
                if standard_files:
                    standard_file = max(standard_files)  # 获取最新的文件
                    standard_df = pd.read_csv(standard_file, encoding='utf-8')

                    print(f"📊 对比结果:")
                    print(f"  方法二记录数: {len(phone_data)}")
                    print(f"  标准程序记录数: {len(standard_df)}")
                    print(f"  记录数差异: {abs(len(phone_data) - len(standard_df))}")

                    if len(phone_data) == len(standard_df):
                        print("🎉 记录数完全匹配！")
                    else:
                        print("⚠️ 记录数存在差异，需要进一步分析")

            else:
                print(f"❌ 标准程序运行失败: {result.stderr}")

        except Exception as e:
            print(f"⚠️ 无法运行标准程序对比: {e}")

        print(f"\n✅ 测试完成！数据已保存到: {filename}")
        return phone_data
    else:
        print("❌ 未获取到任何数据")
        return []


def main():
    """
    主程序入口
    """
    print("=" * 60)
    print("🚀 通用电话信息爬虫程序 - 方法二")
    print("=" * 60)

    # 创建爬虫实例
    crawler = UniversalPhoneCrawler()

    # 可以爬取的单位列表（从CSV文件中读取）
    try:
        df = pd.read_csv('final_units_data.csv', encoding='utf-8')
        available_units = df['单位名称'].tolist()
        print(f"📋 可爬取的单位: {', '.join(available_units[:5])}..." if len(available_units) > 5 else f"📋 可爬取的单位: {', '.join(available_units)}")
    except Exception as e:
        print(f"⚠️ 无法读取单位列表: {e}")
        available_units = ["采矿场"]  # 默认单位

    # 用户选择要爬取的单位
    unit_name = input(f"\n请输入要爬取的单位名称 (默认: 采矿场): ").strip()
    if not unit_name:
        unit_name = "采矿场"

    # 开始爬取
    phone_data = crawler.crawl_unit_phones(unit_name)

    if phone_data:
        # 保存数据
        filename = crawler.save_to_csv(phone_data)

        # 验证数据质量
        validation_result = crawler.validate_data(phone_data)

        print(f"\n🎉 爬取成功！")
        print(f"📊 共获取 {len(phone_data)} 条电话记录")
        print(f"💾 数据已保存到: {filename}")

        # 显示前几条数据作为示例
        print(f"\n📋 数据示例:")
        for i, record in enumerate(phone_data[:3]):
            print(f"  {i+1}. {record['电话号码']} | {record['电话用户名']} | {record['单位名称']}")

        if len(phone_data) > 3:
            print(f"  ... 还有 {len(phone_data) - 3} 条记录")

    else:
        print("❌ 未获取到任何数据，请检查单位名称或网络连接")


if __name__ == "__main__":
    # 如果直接运行此文件，执行测试
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_crawler_with_standard()
    else:
        main()
