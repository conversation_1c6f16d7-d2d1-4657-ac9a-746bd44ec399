#!/usr/bin/env python3
"""
网页爬取和分类脚本
使用crawl4ai API爬取CSV文件中的URL，并对网页进行分类
"""
import csv
import json
import time
import requests
import re
from typing import List, Dict, Tuple
import pandas as pd

class WebCrawlerClassifier:
    def __init__(self, api_base_url: str = "http://172.18.151.239:11235", api_token: str = "123456"):
        self.api_base_url = api_base_url
        self.api_token = api_token
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_token}"
        }
    
    def crawl_url(self, url: str) -> Dict:
        """
        爬取单个URL并返回结果
        """
        crawl_endpoint = f"{self.api_base_url}/crawl"
        
        payload = {
            "urls": [url]
        }
        
        try:
            print(f"正在爬取: {url}")
            
            # 发起爬取请求
            response = requests.post(crawl_endpoint, headers=self.headers, json=payload, timeout=30)
            
            if response.status_code != 200:
                print(f"❌ 爬取失败: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
            
            result = response.json()
            task_id = result.get("task_id")
            
            if not task_id:
                print(f"❌ 未获取到task_id")
                return {"success": False, "error": "No task_id returned"}
            
            # 等待任务完成并获取结果
            return self.get_task_result(task_id, url)
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
            return {"success": False, "error": str(e)}
    
    def get_task_result(self, task_id: str, url: str, max_retries: int = 15) -> Dict:
        """
        获取任务结果
        """
        result_endpoint = f"{self.api_base_url}/task/{task_id}"

        for attempt in range(max_retries):
            try:
                time.sleep(3)  # 等待3秒
                response = requests.get(result_endpoint, headers=self.headers, timeout=30)

                if response.status_code == 200:
                    result = response.json()

                    # 检查任务状态
                    status = result.get("status", "")
                    if status == "completed":
                        print(f"✅ 爬取成功: {url}")
                        # 获取结果数据
                        results = result.get("results", [])
                        if results and len(results) > 0:
                            crawl_result = results[0]
                            return {
                                "success": True,
                                "url": url,
                                "html": crawl_result.get("html", ""),
                                "markdown": crawl_result.get("markdown", ""),
                                "cleaned_html": crawl_result.get("cleaned_html", "")
                            }
                        else:
                            return {"success": False, "error": "No results in completed task"}
                    elif status == "failed":
                        error_msg = result.get("error", "Unknown error")
                        print(f"❌ 任务失败: {error_msg}")
                        return {"success": False, "error": error_msg}
                    else:
                        print(f"⏳ 任务进行中... 状态: {status} (尝试 {attempt + 1}/{max_retries})")
                        continue

                else:
                    print(f"❌ 获取结果失败: {response.status_code} - {response.text}")

            except requests.exceptions.RequestException as e:
                print(f"❌ 获取结果时网络错误: {e}")

        return {"success": False, "error": "Task timeout or failed"}
    
    def classify_content(self, content: str, url: str) -> str:
        """
        根据内容和URL对网页进行分类
        """
        # 基于URL路径的分类
        url_lower = url.lower()
        
        # 生产相关关键词
        production_keywords = [
            "生产", "采矿", "选矿", "化工", "技术", "尾矿", "精尾", "百泰",
            "动力厂", "检化", "地测", "运输"
        ]
        
        # 管理相关关键词  
        management_keywords = [
            "行政", "纪委", "风控", "联络", "企业管理", "党委", "工会", 
            "财务", "供应", "销售", "组织", "团委", "环境", "科技", "质量",
            "宣传", "人力", "安全", "工程", "计划", "发展", "数字化",
            "经营", "服务", "后勤", "宾馆", "保卫", "信息", "档案"
        ]
        
        # 检查URL路径
        if any(keyword in url_lower for keyword in production_keywords):
            return "生产类"
        elif any(keyword in url_lower for keyword in management_keywords):
            return "管理类"
        
        # 如果URL路径无法确定，则基于内容分析
        content_lower = content.lower()
        
        production_score = sum(1 for keyword in production_keywords if keyword in content_lower)
        management_score = sum(1 for keyword in management_keywords if keyword in content_lower)
        
        if production_score > management_score:
            return "生产类"
        else:
            return "管理类"
    
    def process_csv(self, csv_file: str) -> None:
        """
        处理CSV文件中的所有URL
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file, encoding='utf-8')
            print(f"读取到 {len(df)} 条记录")

            # 添加分类列（如果不存在）
            if '分类' not in df.columns:
                df['分类'] = ''

            # 创建输出文件名
            output_file = csv_file.replace('.csv', '_classified.csv')

            # 处理每个URL
            for index, row in df.iterrows():
                unit_name = row['单位名称']
                url = row['子链接']

                print(f"\n处理第 {index + 1}/{len(df)} 条: {unit_name}")

                # 跳过已分类的记录
                if pd.notna(row.get('分类')) and row.get('分类').strip():
                    print(f"已分类，跳过: {row.get('分类')}")
                    continue

                # 爬取URL
                result = self.crawl_url(url)

                if result["success"]:
                    # 分析内容并分类
                    content = result.get("markdown", "") or result.get("cleaned_html", "")
                    classification = self.classify_content(content, url)

                    # 更新DataFrame
                    df.at[index, '分类'] = classification
                    print(f"分类结果: {classification}")

                    # 保存中间结果到新文件
                    try:
                        df.to_csv(output_file, index=False, encoding='utf-8')
                        print(f"中间结果已保存到: {output_file}")
                    except Exception as save_error:
                        print(f"保存中间结果失败: {save_error}")

                else:
                    print(f"爬取失败: {result.get('error', 'Unknown error')}")
                    df.at[index, '分类'] = "爬取失败"

                # 添加延迟避免过于频繁的请求
                time.sleep(1)

            # 最终保存
            try:
                df.to_csv(output_file, index=False, encoding='utf-8')
                print(f"\n✅ 处理完成！结果已保存到 {output_file}")

                # 显示分类统计
                classification_counts = df['分类'].value_counts()
                print("\n分类统计:")
                for category, count in classification_counts.items():
                    print(f"  {category}: {count} 个")
            except Exception as final_save_error:
                print(f"❌ 最终保存失败: {final_save_error}")

        except Exception as e:
            print(f"❌ 处理CSV文件时出错: {e}")

def main():
    """
    主函数
    """
    print("🚀 开始网页爬取和分类任务...")
    
    # 初始化爬虫分类器
    classifier = WebCrawlerClassifier()
    
    # 处理CSV文件
    csv_file = "final_units_data.csv"
    classifier.process_csv(csv_file)

if __name__ == "__main__":
    main()
