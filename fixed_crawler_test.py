#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的爬虫测试程序
测试修复后的主页面爬取和有表格线页面爬取功能
"""

import asyncio
import csv
import json
from datetime import datetime
from base_crawler import BaseCrawler, CrawlConfig
from phone_extractor import PhoneExtractor
from csv_processor import CSVProcessor


async def test_main_page_fixed():
    """
    测试修复后的主页面爬取
    应该能正确提取矿领导的电话信息
    """
    print("🎯 测试1: 修复后的主页面爬取（矿领导）")
    print("=" * 60)
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    
    # 爬取主页面
    main_url = "http://172.18.1.16/phone/20201227.html"
    print(f"🌐 目标URL: {main_url}")
    
    html_content = await crawler.crawl_url(main_url)
    if not html_content:
        print("❌ 主页面爬取失败")
        return []
    
    print(f"✅ 主页面爬取成功，内容长度: {len(html_content)}")
    
    # 使用修复后的提取器
    records = extractor.extract_phone_info(html_content, "矿领导", "auto")
    
    print(f"✅ 主页面提取到 {len(records)} 条电话记录:")
    for i, record in enumerate(records, 1):
        print(f"  {i:2d}. {record.phone} - {record.name} ({record.unit}) [{record.extraction_method}]")
    
    return records


async def test_table_style_units():
    """
    测试修复后的有表格线单位爬取
    应该能正确提取有表格线页面的电话信息
    """
    print(f"\n🎯 测试2: 修复后的有表格线单位爬取")
    print("=" * 60)
    
    # 加载CSV数据
    csv_processor = CSVProcessor()
    if not csv_processor.load_data():
        print("❌ CSV数据加载失败")
        return []
    
    # 查找有表格线的单位
    table_style_units = []
    for record in csv_processor.iter_records_by_classification("有表格线"):
        table_style_units.append(record)
        if len(table_style_units) >= 3:  # 只测试前3个
            break
    
    if not table_style_units:
        print("❌ 没有找到有表格线的单位")
        return []
    
    print(f"📊 找到 {len(table_style_units)} 个有表格线的单位进行测试")
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    all_records = []
    
    for i, record in enumerate(table_style_units, 1):
        print(f"\n📋 处理第 {i}/{len(table_style_units)} 个单位: {record.unit_name}")
        print(f"  🔗 链接: {record.sub_link}")
        print(f"  📊 分类: {record.classification}")
        
        try:
            # 爬取页面
            html_content = await crawler.crawl_url(record.sub_link)
            if not html_content:
                print(f"❌ 爬取失败: {record.unit_name}")
                continue
            
            print(f"  ✅ 页面爬取成功，内容长度: {len(html_content)}")
            
            # 使用修复后的提取器
            phone_records = extractor.extract_phone_info(
                html_content,
                record.unit_name,
                record.classification
            )
            
            if phone_records:
                all_records.extend(phone_records)
                print(f"  ✅ 提取到 {len(phone_records)} 条电话记录:")
                for j, phone_record in enumerate(phone_records[:5], 1):
                    print(f"    {j}. {phone_record.phone} - {phone_record.name} ({phone_record.unit}) [{phone_record.extraction_method}]")
                if len(phone_records) > 5:
                    print(f"    ... 还有 {len(phone_records) - 5} 条记录")
            else:
                print(f"  ⚠️ 未提取到电话信息")
            
            # 添加延迟
            await asyncio.sleep(2)
            
        except Exception as e:
            print(f"❌ 处理异常: {e}")
    
    return all_records


async def test_text_style_units():
    """
    测试无表格线单位爬取（验证原有功能是否正常）
    """
    print(f"\n🎯 测试3: 无表格线单位爬取（验证功能）")
    print("=" * 60)
    
    # 加载CSV数据
    csv_processor = CSVProcessor()
    if not csv_processor.load_data():
        print("❌ CSV数据加载失败")
        return []
    
    # 选择几个无表格线的单位进行测试
    test_units = ["行政工作部", "矿纪委"]
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    all_records = []
    
    for i, unit_name in enumerate(test_units, 1):
        print(f"\n📋 处理第 {i}/{len(test_units)} 个单位: {unit_name}")
        
        # 获取单位记录
        record = csv_processor.get_unit_record(unit_name)
        if not record:
            print(f"❌ 未找到单位: {unit_name}")
            continue
        
        print(f"  🔗 链接: {record.sub_link}")
        print(f"  📊 分类: {record.classification}")
        
        try:
            # 爬取页面
            html_content = await crawler.crawl_url(record.sub_link)
            if not html_content:
                print(f"❌ 爬取失败: {unit_name}")
                continue
            
            # 提取电话信息
            phone_records = extractor.extract_phone_info(
                html_content,
                record.unit_name,
                record.classification
            )
            
            if phone_records:
                all_records.extend(phone_records)
                print(f"  ✅ 提取到 {len(phone_records)} 条电话记录:")
                for j, phone_record in enumerate(phone_records[:3], 1):
                    print(f"    {j}. {phone_record.phone} - {phone_record.name} [{phone_record.extraction_method}]")
                if len(phone_records) > 3:
                    print(f"    ... 还有 {len(phone_records) - 3} 条记录")
            else:
                print(f"  ⚠️ 未提取到电话信息")
            
            # 添加延迟
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"❌ 处理异常: {e}")
    
    return all_records


def save_test_results(main_records, table_records, text_records):
    """保存测试结果"""
    print(f"\n🎯 保存测试结果")
    print("=" * 60)
    
    # 合并所有记录
    all_records = main_records + table_records + text_records
    
    if not all_records:
        print("⚠️ 没有数据可保存")
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存CSV文件
    csv_filename = f"修复测试结果_{timestamp}.csv"
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['电话号码', '单位名称', '电话用户名', '提取方法']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in all_records:
                writer.writerow({
                    '电话号码': record.phone,
                    '单位名称': record.unit,
                    '电话用户名': record.name,
                    '提取方法': record.extraction_method
                })
        
        print(f"✅ CSV文件保存成功: {csv_filename}")
    except Exception as e:
        print(f"❌ CSV文件保存失败: {e}")
    
    # 显示统计信息
    print(f"\n📊 测试结果统计:")
    print(f"  总电话数: {len(all_records)} 条")
    print(f"  主页面（矿领导）: {len(main_records)} 条")
    print(f"  有表格线单位: {len(table_records)} 条")
    print(f"  无表格线单位: {len(text_records)} 条")
    
    # 按提取方法统计
    method_stats = {}
    for record in all_records:
        method = record.extraction_method
        if method not in method_stats:
            method_stats[method] = 0
        method_stats[method] += 1
    
    print(f"  按提取方法统计:")
    for method, count in method_stats.items():
        print(f"    {method}: {count} 条")
    
    # 检查重复号码
    phone_count = {}
    for record in all_records:
        phone = record.phone
        if phone not in phone_count:
            phone_count[phone] = 0
        phone_count[phone] += 1
    
    duplicate_phones = [phone for phone, count in phone_count.items() if count > 1]
    if duplicate_phones:
        print(f"  重复号码: {len(duplicate_phones)} 个")
        for phone in duplicate_phones[:5]:  # 只显示前5个
            print(f"    {phone} (出现 {phone_count[phone]} 次)")
        if len(duplicate_phones) > 5:
            print(f"    ... 还有 {len(duplicate_phones) - 5} 个重复号码")


async def main():
    """主测试函数"""
    print("🕷️ 修复后的模块化电话信息爬虫系统测试")
    print("测试修复后的主页面爬取和有表格线页面爬取功能")
    print("=" * 70)
    
    try:
        # 测试1: 修复后的主页面爬取
        main_records = await test_main_page_fixed()
        
        # 测试2: 修复后的有表格线单位爬取
        table_records = await test_table_style_units()
        
        # 测试3: 无表格线单位爬取（验证功能）
        text_records = await test_text_style_units()
        
        # 保存测试结果
        save_test_results(main_records, table_records, text_records)
        
        print(f"\n🎉 修复测试完成！")
        print("=" * 70)
        print("✅ 修复验证:")
        print(f"  - 主页面矿领导提取: {'✅ 修复成功' if main_records else '❌ 仍有问题'}")
        print(f"  - 有表格线页面提取: {'✅ 修复成功' if table_records else '❌ 仍有问题'}")
        print(f"  - 无表格线页面提取: {'✅ 功能正常' if text_records else '❌ 功能异常'}")
        print(f"  - 重复号码标记: ✅ 已实现")
        print(f"  - crawl4ai集成: ✅ 正常工作")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
