#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全通用电话信息爬虫程序
所有信息都从网页中动态获取，不依赖任何硬编码信息
按照用户描述的表格结构逻辑进行爬取
"""

import requests
import pandas as pd
import re
from bs4 import BeautifulSoup

def get_target_url(unit_name):
    """从final_units_data.csv中获取指定单位的URL"""
    try:
        df = pd.read_csv('final_units_data.csv', encoding='utf-8')
        target_row = df[df['单位名称'] == unit_name]
        if not target_row.empty:
            return target_row.iloc[0]['子链接']
        else:
            print(f"未找到{unit_name}的记录")
            return None
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return None

def crawl_webpage(url):
    """爬取网页内容"""
    try:
        print(f"正在爬取: {url}")
        response = requests.get(url, timeout=10)
        
        # 智能编码检测
        for encoding in ['gb2312', 'gbk', 'utf-8']:
            response.encoding = encoding
            if '�' not in response.text:
                break
        
        return response.text
    except Exception as e:
        print(f"爬取失败: {e}")
        return None

def extract_phone_data_fully_universal(html_content, base_unit_name):
    """完全通用的电话数据提取方法"""
    soup = BeautifulSoup(html_content, 'html.parser')
    phone_data = []

    # 查找所有带边框的表格
    tables = soup.find_all('table', {'border': '1'})
    print(f"发现 {len(tables)} 个表格")

    # 用于存储已找到的第二层单位名称，供后续表格复用
    last_found_second_level = None

    for table_idx, table in enumerate(tables):
        print(f"\n处理表格 {table_idx}:")

        # 动态获取表格上方的第二层单位名称
        second_level_name = get_second_level_name_from_table(table, soup)

        if second_level_name:
            print(f"  找到第二层单位名称: {second_level_name}")
            last_found_second_level = second_level_name
        else:
            # 如果当前表格没有找到，使用最近找到的第二层单位名称
            if last_found_second_level:
                second_level_name = last_found_second_level
                print(f"  使用最近的第二层单位名称: {second_level_name}")
            else:
                print(f"  未找到任何第二层单位名称，将使用两层结构")

        # 检查表格是否包含电话信息
        if not has_phone_data(table):
            print(f"  跳过：表格不包含电话信息")
            continue

        # 提取表格数据
        table_data = extract_table_data_universal(table, base_unit_name, second_level_name)
        phone_data.extend(table_data)

    return phone_data

def get_second_level_name_from_table(table, soup):
    """动态获取表格上方的第二层单位名称"""
    # 方法1：查找表格前面紧挨着的文本元素
    current = table.previous_sibling

    # 向前查找最多10个兄弟元素
    search_count = 0
    while current and search_count < 10:
        if hasattr(current, 'get_text'):
            text = current.get_text(strip=True)

            # 检查是否为8个中文字以内的有效单位名称
            if text and len(text) <= 16:  # 8个中文字约16个字符
                # 检查是否包含中文且不包含电话号码和冒号
                if (re.search(r'[\u4e00-\u9fa5]', text) and
                    not re.search(r'\d{7,11}', text) and
                    ':' not in text and '：' not in text):
                    # 过滤掉导航文本和无关文本
                    if not any(keyword in text for keyword in ['网站首页', '网上服务', '当前位置', '电话号码']):
                        print(f"    找到第二层单位名称: {text}")
                        return text

        current = current.previous_sibling
        search_count += 1

    # 方法2：查找表格所在的父元素中的文本
    parent = table.parent
    if parent:
        # 查找父元素中表格前面的文本
        for child in parent.children:
            if child == table:
                break
            if hasattr(child, 'get_text'):
                text = child.get_text(strip=True)
                if text and len(text) <= 16:
                    if (re.search(r'[\u4e00-\u9fa5]', text) and
                        not re.search(r'\d{7,11}', text) and
                        ':' not in text and '：' not in text):
                        if not any(keyword in text for keyword in ['网站首页', '网上服务', '当前位置', '电话号码']):
                            print(f"    从父元素找到第二层单位名称: {text}")
                            return text

    # 方法3：查找表格前面的所有文本节点（限制在更近的范围内）
    all_text_before = []
    for element in soup.find_all(text=True):
        if element.parent and element.parent.name not in ['script', 'style']:
            text = element.strip()
            if text and len(text) <= 16:
                if (re.search(r'[\u4e00-\u9fa5]', text) and
                    not re.search(r'\d{7,11}', text) and
                    ':' not in text and '：' not in text):
                    if not any(keyword in text for keyword in ['网站首页', '网上服务', '当前位置', '电话号码']):
                        # 检查这个文本是否在表格之前且距离很近
                        try:
                            element_pos = str(soup).find(str(element))
                            table_pos = str(soup).find(str(table))
                            distance = table_pos - element_pos
                            # 只考虑距离很近的文本（500字符以内），避免找到其他表格的标题
                            if element_pos < table_pos and distance < 500:
                                all_text_before.append((text, distance))
                        except:
                            continue

    # 选择距离表格最近的文本
    if all_text_before:
        all_text_before.sort(key=lambda x: x[1])  # 按距离排序
        closest_text = all_text_before[0][0]
        print(f"    从全局搜索找到第二层单位名称: {closest_text}")
        return closest_text

    # 如果没找到，返回None，表示没有第二层单位名称
    return None

def has_phone_data(table):
    """检查表格是否包含电话数据"""
    table_text = table.get_text()
    return bool(re.search(r'\b(\d{7}|\d{11})\b', table_text))

def extract_table_data_universal(table, base_unit_name, second_level_name):
    """通用表格数据提取方法"""
    phone_data = []
    rows = table.find_all('tr')

    if len(rows) <= 1:
        return phone_data

    # 第一行是列头
    header_row = rows[0]
    header_cells = header_row.find_all(['td', 'th'])

    # 提取列头名称
    column_headers = []
    for cell in header_cells:
        header_text = cell.get_text(strip=True)
        # 移除HTML标签如<strong>
        clean_header = re.sub(r'<[^>]+>', '', header_text)
        column_headers.append(clean_header)

    print(f"    识别到列头: {column_headers}")

    # 处理数据行
    data_rows = rows[1:]

    # 按列处理数据
    for col_idx, column_header in enumerate(column_headers):
        if not column_header:
            continue

        print(f"    处理列 {col_idx}: {column_header}")

        # 提取该列的所有数据
        col_data = extract_column_data_new(data_rows, col_idx, base_unit_name, second_level_name, column_header)
        phone_data.extend(col_data)

    return phone_data

def extract_column_data_new(data_rows, col_idx, base_unit_name, second_level_name, column_header):
    """完全通用的列数据提取方法，动态识别子列"""
    phone_data = []
    row_idx = 0

    while row_idx < len(data_rows):
        row = data_rows[row_idx]
        cells = row.find_all(['td', 'th'])

        if col_idx >= len(cells):
            row_idx += 1
            continue

        cell = cells[col_idx]
        cell_text = cell.get_text(strip=True)

        # 跳过空单元格
        if not cell_text or cell_text == '&nbsp;':
            row_idx += 1
            continue

        # 检查是否为子列标题（不以7位或11位数字结尾的文字）
        if is_sub_column_title(cell_text):
            # 这是一个子列标题
            sub_column_name = extract_sub_column_name(cell_text)
            print(f"      发现子列: {sub_column_name}")

            # 提取该子列下的所有电话数据
            sub_data, next_row_idx = extract_sub_column_phone_data(
                data_rows, row_idx + 1, col_idx, base_unit_name, second_level_name, sub_column_name
            )
            phone_data.extend(sub_data)
            row_idx = next_row_idx
        else:
            # 这是电话数据行，使用原始列头作为单位名称
            phone_matches = re.findall(r'\b(\d{7}|\d{11})\b', cell_text)

            for phone in phone_matches:
                user_name = extract_user_name_universal(cell_text, phone)

                # 根据是否有第二层单位名称构建单位名称
                if second_level_name:
                    unit_name = f"{base_unit_name}-{second_level_name}-{column_header}"
                else:
                    unit_name = f"{base_unit_name}-{column_header}"

                phone_data.append({
                    '电话号码': phone,
                    '电话用户名': user_name,
                    '单位名称': unit_name
                })

                print(f"      找到: {phone} | {user_name} | {unit_name}")

            row_idx += 1

    return phone_data

def is_sub_column_title(text):
    """判断是否为子列标题"""
    # 不以7位或11位数字结尾
    if re.search(r'\d{7}$|\d{11}$', text):
        return False

    # 不包含冒号（冒号通常表示电话号码行）
    if ':' in text or '：' in text:
        return False

    # 包含中文字符
    if not re.search(r'[\u4e00-\u9fa5]', text):
        return False

    # 长度合理（1-20个字符）
    if len(text) < 1 or len(text) > 20:
        return False

    # 不是纯数字
    if text.isdigit():
        return False

    return True

def extract_sub_column_name(text):
    """提取子列名称"""
    # 移除HTML标签和多余的空白
    clean_text = re.sub(r'<[^>]+>', '', text).strip()

    # 提取中文字符和数字
    clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()]', '', clean_text)

    return clean_text if clean_text else text

def extract_sub_column_phone_data(data_rows, start_row_idx, col_idx, base_unit_name, second_level_name, sub_column_name):
    """提取子列下的电话数据"""
    phone_data = []
    row_idx = start_row_idx

    while row_idx < len(data_rows):
        row = data_rows[row_idx]
        cells = row.find_all(['td', 'th'])

        if col_idx >= len(cells):
            row_idx += 1
            continue

        cell = cells[col_idx]
        cell_text = cell.get_text(strip=True)

        # 如果遇到空单元格，跳过
        if not cell_text or cell_text == '&nbsp;':
            row_idx += 1
            continue

        # 如果遇到新的子列标题，结束当前子列
        if is_sub_column_title(cell_text):
            break

        # 提取电话号码
        phone_matches = re.findall(r'\b(\d{7}|\d{11})\b', cell_text)

        for phone in phone_matches:
            user_name = extract_user_name_universal(cell_text, phone)

            # 根据是否有第二层单位名称构建单位名称
            if second_level_name:
                unit_name = f"{base_unit_name}-{second_level_name}-{sub_column_name}"
            else:
                unit_name = f"{base_unit_name}-{sub_column_name}"

            phone_data.append({
                '电话号码': phone,
                '电话用户名': user_name,
                '单位名称': unit_name
            })

            print(f"        找到: {phone} | {user_name} | {unit_name}")

        row_idx += 1

    return phone_data, row_idx

def extract_main_column_data(data_rows, col_idx, base_unit_name, second_level_name):
    """提取主列数据"""
    phone_data = []
    row_idx = 0
    
    while row_idx < len(data_rows):
        row = data_rows[row_idx]
        cells = row.find_all(['td', 'th'])
        
        if col_idx >= len(cells):
            row_idx += 1
            continue
        
        cell = cells[col_idx]
        cell_text = cell.get_text(strip=True)
        
        # 跳过空单元格
        if not cell_text or cell_text == '&nbsp;':
            row_idx += 1
            continue
        
        # 检查是否为子列名称（不以7位或11位数字结尾）
        if is_sub_column_name(cell_text):
            third_level_name = extract_third_level_name(cell_text)
            print(f"      找到子列: {third_level_name}")
            
            # 提取该子列下的电话数据
            sub_col_data, next_row_idx = extract_sub_column_data(
                data_rows, row_idx + 1, col_idx, base_unit_name, second_level_name, third_level_name
            )
            phone_data.extend(sub_col_data)
            row_idx = next_row_idx
        else:
            row_idx += 1
    
    return phone_data

def is_sub_column_name(text):
    """判断是否为子列名称"""
    # 不以7位或11位数字结尾
    if re.search(r'\d{7}$|\d{11}$', text):
        return False

    # 包含中文字符
    if not re.search(r'[\u4e00-\u9fa5]', text):
        return False

    # 不包含冒号（冒号通常表示电话号码行）
    if ':' in text or '：' in text:
        return False

    # 长度合理（1-20个字符）
    if len(text) < 1 or len(text) > 20:
        return False

    # 不是纯数字
    if text.isdigit():
        return False

    return True

def extract_third_level_name(text):
    """提取第三层单位名称"""
    # 移除冒号和其他标点符号
    clean_text = re.sub(r'[:：\s]+$', '', text)
    
    # 提取中文字符和数字
    clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()]', '', clean_text)
    
    return clean_text if clean_text else text

def extract_sub_column_data(data_rows, start_row_idx, col_idx, base_unit_name, second_level_name, third_level_name):
    """提取子列下的电话数据"""
    phone_data = []
    row_idx = start_row_idx
    
    while row_idx < len(data_rows):
        row = data_rows[row_idx]
        cells = row.find_all(['td', 'th'])
        
        # 检查是否超出列范围
        if col_idx >= len(cells):
            break
        
        cell = cells[col_idx]
        cell_text = cell.get_text(strip=True)
        
        # 检查子列结束条件
        if is_sub_column_end(cell_text, data_rows, row_idx, col_idx):
            break
        
        # 跳过空行
        if not cell_text or cell_text == '&nbsp;':
            row_idx += 1
            continue
        
        # 提取电话信息
        phone_matches = re.findall(r'\b(\d{7}|\d{11})\b', cell_text)
        
        for phone in phone_matches:
            user_name = extract_user_name_universal(cell_text, phone)
            unit_name = f"{base_unit_name}-{second_level_name}-{third_level_name}"
            
            phone_data.append({
                '电话号码': phone,
                '电话用户名': user_name,
                '单位名称': unit_name
            })
            
            print(f"        找到: {phone} | {user_name} | {unit_name}")
        
        row_idx += 1
    
    return phone_data, row_idx

def is_sub_column_end(cell_text, data_rows, current_row_idx, col_idx):
    """判断子列是否结束"""
    # 如果当前单元格为空，继续处理
    if not cell_text or cell_text == '&nbsp;':
        return False

    # 检查下一行
    if current_row_idx + 1 < len(data_rows):
        next_row = data_rows[current_row_idx + 1]
        next_cells = next_row.find_all(['td', 'th'])

        if col_idx < len(next_cells):
            next_cell_text = next_cells[col_idx].get_text(strip=True)

            # 下一行为空，继续处理当前子列
            if not next_cell_text or next_cell_text == '&nbsp;':
                return False

            # 下一行是新的子列名称
            if is_sub_column_name(next_cell_text):
                return True
        else:
            # 下一行没有这一列，子列结束
            return True
    else:
        # 没有下一行，子列结束
        return True

    return False

def extract_user_name_universal(cell_text, phone):
    """通用用户名提取方法"""
    # 清理文本
    cell_text = re.sub(r'\s+', ' ', cell_text.strip())
    
    # 方法1：查找电话号码前的内容
    phone_pattern = re.escape(phone)
    pattern1 = r'([^:：]+?)[\s]*[:：]\s*' + phone_pattern
    match1 = re.search(pattern1, cell_text)
    
    if match1:
        user_name = match1.group(1).strip()
        # 保留中文、英文、数字和常用符号
        user_name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()１２３４５６７８９０\-/]', '', user_name)
        if user_name:
            return user_name
    
    # 方法2：分割后查找
    parts = cell_text.split(phone)
    if len(parts) > 1:
        before_phone = parts[0].strip()
        before_phone = re.sub(r'[:：\s]+$', '', before_phone)
        cleaned_name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()１２３４５６７８９０\-/]', '', before_phone)
        if cleaned_name:
            return cleaned_name
    
    # 方法3：提取中文内容
    text_without_phone = cell_text.replace(phone, '')
    cleaned_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9（）()１２３４５６７８９０\-/]', '', text_without_phone)
    if cleaned_text:
        return cleaned_text.strip()
    
    return "未知用户"

def mark_duplicate_phones(df):
    """为重复的电话号码添加星号标记"""
    df_marked = df.copy()
    
    phone_counts = df['电话号码'].value_counts()
    duplicate_phones = phone_counts[phone_counts > 1].index.tolist()
    
    for phone in duplicate_phones:
        phone_rows = df_marked[df_marked['电话号码'] == phone].index.tolist()
        
        for i, row_idx in enumerate(phone_rows):
            if i > 0:
                stars = '*' * i
                df_marked.loc[row_idx, '电话号码'] = f"{phone}{stars}"
    
    return df_marked

def save_to_csv(phone_data, unit_name, filename=None):
    """保存电话数据到CSV文件"""
    if not phone_data:
        print("没有找到电话数据")
        return

    if not filename:
        # 使用时间戳避免文件名冲突
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'{unit_name}_电话信息_完全通用_{timestamp}.csv'

    df = pd.DataFrame(phone_data)
    df_marked = mark_duplicate_phones(df)

    try:
        df_marked.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"已保存 {len(df_marked)} 条电话记录到 {filename}")
    except PermissionError:
        # 如果文件被占用，尝试使用备用文件名
        backup_filename = f'{unit_name}_电话信息_完全通用_backup_{timestamp}.csv'
        df_marked.to_csv(backup_filename, index=False, encoding='utf-8-sig')
        print(f"原文件被占用，已保存到备用文件: {backup_filename}")
        filename = backup_filename

    # 显示重复统计
    duplicates = df[df.duplicated(subset=['电话号码'], keep=False)].sort_values('电话号码')
    if len(duplicates) > 0:
        phone_counts = {}
        for _, row in duplicates.iterrows():
            phone = row['电话号码']
            phone_counts[phone] = phone_counts.get(phone, 0) + 1

        print(f"\n发现重复电话号码，已添加星号标记:")
        for phone, count in phone_counts.items():
            print(f"  {phone}: {count}次重复")

    return df_marked

def main():
    """主函数"""
    target_unit = "采矿场"
    
    print(f"开始完全通用爬虫程序，目标单位: {target_unit}")
    print("=" * 60)
    
    # 获取目标URL
    target_url = get_target_url(target_unit)
    if not target_url:
        return
    
    print(f"目标URL: {target_url}")
    
    # 爬取网页内容
    html_content = crawl_webpage(target_url)
    if not html_content:
        return
    
    print("网页内容获取成功，开始完全通用分析...")
    
    # 完全通用提取电话数据
    phone_data = extract_phone_data_fully_universal(html_content, target_unit)
    
    if phone_data:
        print(f"\n成功提取 {len(phone_data)} 条电话记录")
        
        # 保存到CSV
        df = save_to_csv(phone_data, target_unit)
        
        # 显示前10条记录
        print("\n前10条记录示例：")
        for i, record in enumerate(phone_data[:10]):
            print(f"{i+1}. 电话: {record['电话号码']}, "
                  f"用户: {record['电话用户名']}, "
                  f"单位: {record['单位名称']}")
        
        # 显示统计信息
        print(f"\n统计信息:")
        print(f"总记录数: {len(phone_data)}")
        print(f"处理后记录数: {len(df)}")
        
        # 按第二层单位分组统计
        if len(df) > 0:
            second_level_counts = df['单位名称'].str.split('-').str[1].value_counts()
            print(f"\n各部门电话数量:")
            for unit, count in second_level_counts.items():
                print(f"  {unit}: {count}条")
        
    else:
        print("未找到电话信息")

if __name__ == "__main__":
    main()
