#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示爬虫程序
快速演示模块化爬虫系统的功能
"""

import asyncio
import csv
import json
from datetime import datetime
from base_crawler import BaseCrawler, CrawlConfig
from phone_extractor import PhoneExtractor
from csv_processor import CSVProcessor


async def demo_main_page():
    """演示主页面爬取"""
    print("🎯 步骤1: 爬取主页面电话信息")
    print("=" * 50)
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    
    # 爬取主页面
    main_url = "http://172.18.1.16/phone/20201227.html"
    print(f"🌐 目标URL: {main_url}")
    
    html_content = await crawler.crawl_url(main_url)
    if not html_content:
        print("❌ 主页面爬取失败")
        return []
    
    # 提取电话信息
    records = extractor.extract_phone_info(html_content, "矿领导", "auto")
    
    print(f"✅ 主页面提取到 {len(records)} 条电话记录:")
    for i, record in enumerate(records, 1):
        print(f"  {i:2d}. {record.phone} - {record.name} ({record.unit})")
    
    return records


async def demo_sample_units():
    """演示样本单位爬取"""
    print(f"\n🎯 步骤2: 爬取样本单位电话信息")
    print("=" * 50)
    
    # 加载CSV数据
    csv_processor = CSVProcessor()
    if not csv_processor.load_data():
        print("❌ CSV数据加载失败")
        return []
    
    # 显示统计信息
    summary = csv_processor.get_summary()
    print(f"📊 CSV数据统计:")
    print(f"  总单位数: {summary['total_rows']}")
    for classification, count in summary['classification_counts'].items():
        print(f"  {classification}: {count} 个单位")
    
    # 选择样本单位进行演示
    sample_units = [
        "行政工作部",  # 无表格线
        "矿纪委",      # 无表格线
        "风控内审部",  # 无表格线
    ]
    
    crawler = BaseCrawler()
    extractor = PhoneExtractor()
    all_records = []
    
    for i, unit_name in enumerate(sample_units, 1):
        print(f"\n📋 处理第 {i}/{len(sample_units)} 个单位: {unit_name}")
        
        # 获取单位记录
        record = csv_processor.get_unit_record(unit_name)
        if not record:
            print(f"❌ 未找到单位: {unit_name}")
            continue
        
        print(f"  🔗 链接: {record.sub_link}")
        print(f"  📊 分类: {record.classification}")
        
        try:
            # 爬取页面
            html_content = await crawler.crawl_url(record.sub_link)
            if not html_content:
                print(f"❌ 爬取失败: {unit_name}")
                continue
            
            # 提取电话信息
            phone_records = extractor.extract_phone_info(
                html_content,
                record.unit_name,
                record.classification
            )
            
            if phone_records:
                all_records.extend(phone_records)
                print(f"  ✅ 提取到 {len(phone_records)} 条电话记录:")
                for j, phone_record in enumerate(phone_records[:3], 1):
                    print(f"    {j}. {phone_record.phone} - {phone_record.name}")
                if len(phone_records) > 3:
                    print(f"    ... 还有 {len(phone_records) - 3} 条记录")
            else:
                print(f"  ⚠️ 未提取到电话信息")
            
            # 添加延迟
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"❌ 处理异常: {e}")
    
    return all_records


def save_demo_results(main_records, unit_records):
    """保存演示结果"""
    print(f"\n🎯 步骤3: 保存演示结果")
    print("=" * 50)
    
    # 合并所有记录
    all_records = main_records + unit_records
    
    if not all_records:
        print("⚠️ 没有数据可保存")
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存CSV文件
    csv_filename = f"演示结果_{timestamp}.csv"
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['电话号码', '单位名称', '电话用户名', '提取方法']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in all_records:
                writer.writerow({
                    '电话号码': record.phone,
                    '单位名称': record.unit,
                    '电话用户名': record.name,
                    '提取方法': record.extraction_method
                })
        
        print(f"✅ CSV文件保存成功: {csv_filename}")
    except Exception as e:
        print(f"❌ CSV文件保存失败: {e}")
    
    # 保存JSON文件
    json_filename = f"演示结果_{timestamp}.json"
    try:
        data = []
        for record in all_records:
            data.append({
                '电话号码': record.phone,
                '单位名称': record.unit,
                '电话用户名': record.name,
                '提取方法': record.extraction_method
            })
        
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ JSON文件保存成功: {json_filename}")
    except Exception as e:
        print(f"❌ JSON文件保存失败: {e}")
    
    # 显示统计信息
    print(f"\n📊 演示结果统计:")
    print(f"  总电话数: {len(all_records)} 条")
    
    # 按单位分组统计
    unit_stats = {}
    for record in all_records:
        unit = record.unit
        if unit not in unit_stats:
            unit_stats[unit] = 0
        unit_stats[unit] += 1
    
    print(f"  按单位统计:")
    for unit, count in unit_stats.items():
        print(f"    {unit}: {count} 条")
    
    # 按提取方法统计
    method_stats = {}
    for record in all_records:
        method = record.extraction_method
        if method not in method_stats:
            method_stats[method] = 0
        method_stats[method] += 1
    
    print(f"  按提取方法统计:")
    for method, count in method_stats.items():
        print(f"    {method}: {count} 条")


async def main():
    """主演示函数"""
    print("🕷️ 模块化电话信息爬虫系统 - 演示版")
    print("基于crawl4ai的通用电话信息提取工具")
    print("支持有表格线和无表格线两种网页样式")
    print("=" * 60)
    
    try:
        # 步骤1: 爬取主页面
        main_records = await demo_main_page()
        
        # 步骤2: 爬取样本单位
        unit_records = await demo_sample_units()
        
        # 步骤3: 保存结果
        save_demo_results(main_records, unit_records)
        
        print(f"\n🎉 演示完成！")
        print("=" * 60)
        print("✅ 主要功能验证:")
        print("  - crawl4ai连接和爬取 ✅")
        print("  - 主页面电话提取 ✅")
        print("  - CSV数据处理 ✅")
        print("  - 无表格线页面电话提取 ✅")
        print("  - 结果保存和统计 ✅")
        print("\n📝 说明:")
        print("  - 本演示仅处理了3个样本单位")
        print("  - 完整版本可处理所有42个单位")
        print("  - 支持有表格线和无表格线两种样式")
        print("  - 具备完整的错误处理和日志记录")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
