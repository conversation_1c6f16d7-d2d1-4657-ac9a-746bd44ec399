#!/usr/bin/env python3
"""
自动化表格边框分类测试
"""
from table_border_classifier import CrawlConfig, TableBorderCrawlerSystem

def main():
    """自动化测试主函数"""
    print("🚀 自动化表格边框样式分类测试")
    print("=" * 50)
    
    # 配置系统
    config = CrawlConfig()
    system = TableBorderCrawlerSystem(config)
    
    # 使用默认参数
    source_url = "http://172.18.1.16/phone/20201227.html"
    output_file = "final_units_data.csv"
    
    print(f"源URL: {source_url}")
    print(f"输出文件: {output_file}")
    print()
    
    # 开始处理
    try:
        system.extract_and_classify_links(source_url, output_file)
        print("\n✅ 处理完成！")
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")

if __name__ == "__main__":
    main()
