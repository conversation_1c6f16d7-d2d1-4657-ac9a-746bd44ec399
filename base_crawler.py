#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础爬虫模块
提供统一的crawl4ai接口和错误处理机制
"""

import asyncio
import aiohttp
import logging
import time
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class CrawlConfig:
    """爬虫配置类"""
    api_base_url: str = "http://172.18.151.239:11235"
    api_token: str = "123456"
    max_retries: int = 15
    retry_delay: float = 3.0
    request_timeout: int = 60
    rate_limit_delay: float = 1.0


class BaseCrawler:
    """
    基础爬虫类
    
    提供统一的crawl4ai接口，包含：
    1. 连接测试
    2. 网页爬取（支持同步和异步模式）
    3. 错误处理和重试机制
    4. 日志记录
    """
    
    def __init__(self, config: CrawlConfig = None):
        """初始化爬虫"""
        self.config = config or CrawlConfig()
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    async def test_connection(self) -> bool:
        """
        测试crawl4ai服务连接状态
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            async with aiohttp.ClientSession() as session:
                health_url = f"{self.config.api_base_url}/health"
                async with session.get(health_url, timeout=10) as response:
                    if response.status == 200:
                        self.logger.info("✅ crawl4ai服务连接成功")
                        return True
                    else:
                        self.logger.error(f"❌ crawl4ai连接失败: {response.status}")
                        return False
        except Exception as e:
            self.logger.error(f"❌ crawl4ai连接异常: {e}")
            return False
    
    async def crawl_url(self, url: str) -> Optional[str]:
        """
        爬取指定URL的网页内容
        
        Args:
            url: 要爬取的网页URL
            
        Returns:
            Optional[str]: 成功返回HTML内容，失败返回None
        """
        self.logger.info(f"🌐 开始爬取: {url}")
        
        # 首先测试连接
        if not await self.test_connection():
            self.logger.error("❌ crawl4ai服务不可用")
            return None
        
        # 尝试不同的API调用方式
        html_content = await self._try_simple_api(url)
        if not html_content:
            html_content = await self._try_post_api(url)
        
        if html_content:
            self.logger.info(f"✅ 爬取成功，内容长度: {len(html_content)} 字符")
        else:
            self.logger.error(f"❌ 爬取失败: {url}")
        
        return html_content
    
    async def _try_simple_api(self, url: str) -> Optional[str]:
        """尝试简单的GET API"""
        try:
            async with aiohttp.ClientSession() as session:
                crawl_url = f"{self.config.api_base_url}/crawl"
                params = {
                    'url': url,
                    'format': 'html'
                }
                
                self.logger.info("🔄 尝试简单API调用...")
                async with session.get(
                    crawl_url, 
                    params=params, 
                    timeout=self.config.request_timeout
                ) as response:
                    if response.status == 200:
                        content = await response.text()
                        self.logger.info("✅ 简单API调用成功")
                        return content
                    else:
                        self.logger.warning(f"⚠️ 简单API调用失败: {response.status}")
                        return None
        except Exception as e:
            self.logger.warning(f"⚠️ 简单API调用异常: {e}")
            return None
    
    async def _try_post_api(self, url: str) -> Optional[str]:
        """尝试POST API（推荐方式）"""
        try:
            async with aiohttp.ClientSession() as session:
                api_url = f"{self.config.api_base_url}/crawl"
                payload = {
                    'urls': [url],
                    'bypass_cache': True,
                    'include_raw_html': True
                }
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.config.api_token}'
                }
                
                self.logger.info("🔄 尝试POST API调用...")
                async with session.post(
                    api_url, 
                    json=payload, 
                    headers=headers, 
                    timeout=self.config.request_timeout
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        # 处理不同的响应格式
                        if 'task_id' in result:
                            # 异步任务模式
                            task_id = result['task_id']
                            self.logger.info(f"📋 获得任务ID: {task_id}")
                            return await self._wait_for_task(session, task_id)
                        elif result.get('results'):
                            # 同步返回结果
                            return self._extract_html_from_result(result['results'][0])
                        else:
                            self.logger.error("❌ POST API无法获取内容")
                            return None
                    else:
                        error_text = await response.text()
                        self.logger.error(f"❌ POST API失败: {response.status} - {error_text}")
                        return None
        except Exception as e:
            self.logger.error(f"❌ POST API异常: {e}")
            return None
    
    async def _wait_for_task(self, session: aiohttp.ClientSession, task_id: str) -> Optional[str]:
        """等待异步任务完成"""
        try:
            status_url = f"{self.config.api_base_url}/task/{task_id}"
            headers = {'Authorization': f'Bearer {self.config.api_token}'}
            
            for attempt in range(self.config.max_retries):
                await asyncio.sleep(self.config.retry_delay)
                
                self.logger.info(f"🔍 检查任务状态 ({attempt + 1}/{self.config.max_retries})")
                
                async with session.get(status_url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        result = await response.json()
                        status = result.get('status', 'unknown')
                        
                        if status == 'completed':
                            self.logger.info("✅ 异步任务完成")
                            return self._extract_html_from_task_result(result)
                        elif status == 'failed':
                            self.logger.error("❌ 异步任务失败")
                            return None
                        # 继续等待其他状态
                    else:
                        self.logger.warning(f"⚠️ 无法获取任务状态: {response.status}")
            
            self.logger.error("❌ 异步任务超时")
            return None
        except Exception as e:
            self.logger.error(f"❌ 等待任务异常: {e}")
            return None
    
    def _extract_html_from_result(self, result: Dict[str, Any]) -> Optional[str]:
        """从结果中提取HTML内容"""
        html_content = (
            result.get('cleaned_html') or
            result.get('raw_html') or
            result.get('html', '')
        )
        return html_content if html_content else None
    
    def _extract_html_from_task_result(self, result: Dict[str, Any]) -> Optional[str]:
        """从任务结果中提取HTML内容"""
        # 尝试不同的结果路径
        html_content = None
        
        # 路径1: result.result.results[0]
        if result.get('result') and result['result'].get('results'):
            first_result = result['result']['results'][0]
            html_content = self._extract_html_from_result(first_result)
        
        # 路径2: result.results[0]
        elif result.get('results'):
            first_result = result['results'][0]
            html_content = self._extract_html_from_result(first_result)
        
        # 路径3: 直接在result中
        elif result.get('cleaned_html') or result.get('raw_html') or result.get('html'):
            html_content = self._extract_html_from_result(result)
        
        return html_content


# 便捷函数
async def crawl_url_simple(url: str, config: CrawlConfig = None) -> Optional[str]:
    """
    简单的URL爬取函数
    
    Args:
        url: 要爬取的URL
        config: 爬虫配置，可选
        
    Returns:
        Optional[str]: HTML内容或None
    """
    crawler = BaseCrawler(config)
    return await crawler.crawl_url(url)


if __name__ == "__main__":
    # 测试代码
    async def test():
        crawler = BaseCrawler()
        
        # 测试连接
        if await crawler.test_connection():
            print("连接测试成功")
            
            # 测试爬取
            test_url = "http://172.18.1.16/phone/20201227.html"
            html = await crawler.crawl_url(test_url)
            if html:
                print(f"爬取成功，内容长度: {len(html)}")
            else:
                print("爬取失败")
        else:
            print("连接测试失败")
    
    asyncio.run(test())
