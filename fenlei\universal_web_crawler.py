#!/usr/bin/env python3
"""
通用网页爬虫和分类系统
Universal Web Crawler and Classification System

功能特点：
1. 模块化设计，易于扩展
2. 支持多种分类策略
3. 自动链接提取
4. 错误处理和重试机制
5. 进度跟踪和日志记录
"""

import requests
import time
import json
import csv
import logging
import re
from typing import List, Dict, Optional, Tuple, Any
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
from abc import ABC, abstractmethod
from bs4 import BeautifulSoup
import pandas as pd


@dataclass
class CrawlConfig:
    """爬虫配置类"""
    api_base_url: str = "http://172.18.151.239:11235"
    api_token: str = "123456"
    max_retries: int = 15
    retry_delay: float = 3.0
    request_timeout: int = 30
    rate_limit_delay: float = 1.0


@dataclass
class CrawlResult:
    """爬取结果数据类"""
    url: str
    success: bool
    html: str = ""
    markdown: str = ""
    cleaned_html: str = ""
    error_message: str = ""
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class LinkExtractor:
    """链接提取器"""
    
    @staticmethod
    def extract_links_from_html(html_content: str, base_url: str) -> List[Dict[str, str]]:
        """从HTML中提取链接"""
        soup = BeautifulSoup(html_content, 'html.parser')
        links = []
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            text = link.get_text(strip=True)
            
            # 处理相对链接
            absolute_url = urljoin(base_url, href)
            
            # 过滤掉无效链接
            if LinkExtractor._is_valid_link(absolute_url, text):
                links.append({
                    'text': text,
                    'url': absolute_url,
                    'original_href': href
                })
        
        return links
    
    @staticmethod
    def _is_valid_link(url: str, text: str) -> bool:
        """验证链接是否有效"""
        # 过滤掉空文本或无意义的链接
        if not text or len(text.strip()) < 2:
            return False
        
        # 过滤掉JavaScript链接和锚点
        if url.startswith(('javascript:', 'mailto:', '#')):
            return False
        
        # 确保是HTTP/HTTPS链接
        parsed = urlparse(url)
        if not parsed.scheme in ['http', 'https']:
            return False
        
        return True


class WebCrawler:
    """网页爬虫核心类"""
    
    def __init__(self, config: CrawlConfig):
        self.config = config
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.api_token}"
        }
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('WebCrawler')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def crawl_url(self, url: str) -> CrawlResult:
        """爬取单个URL"""
        self.logger.info(f"开始爬取: {url}")
        
        try:
            # 发起爬取请求
            task_id = self._submit_crawl_task(url)
            if not task_id:
                return CrawlResult(url=url, success=False, error_message="Failed to submit crawl task")
            
            # 获取爬取结果
            return self._get_crawl_result(task_id, url)
            
        except Exception as e:
            self.logger.error(f"爬取URL时发生错误 {url}: {e}")
            return CrawlResult(url=url, success=False, error_message=str(e))
    
    def _submit_crawl_task(self, url: str) -> Optional[str]:
        """提交爬取任务"""
        crawl_endpoint = f"{self.config.api_base_url}/crawl"
        payload = {"urls": [url]}
        
        try:
            response = requests.post(
                crawl_endpoint, 
                headers=self.headers, 
                json=payload, 
                timeout=self.config.request_timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("task_id")
            else:
                self.logger.error(f"提交任务失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"网络请求错误: {e}")
            return None
    
    def _get_crawl_result(self, task_id: str, url: str) -> CrawlResult:
        """获取爬取结果"""
        result_endpoint = f"{self.config.api_base_url}/task/{task_id}"
        
        for attempt in range(self.config.max_retries):
            try:
                time.sleep(self.config.retry_delay)
                response = requests.get(
                    result_endpoint, 
                    headers=self.headers, 
                    timeout=self.config.request_timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    status = result.get("status", "")
                    
                    if status == "completed":
                        self.logger.info(f"爬取成功: {url}")
                        return self._parse_completed_result(result, url)
                    elif status == "failed":
                        error_msg = result.get("error", "Unknown error")
                        self.logger.error(f"任务失败: {error_msg}")
                        return CrawlResult(url=url, success=False, error_message=error_msg)
                    else:
                        self.logger.info(f"任务进行中... 状态: {status} (尝试 {attempt + 1}/{self.config.max_retries})")
                        continue
                else:
                    self.logger.error(f"获取结果失败: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"获取结果时网络错误: {e}")
        
        return CrawlResult(url=url, success=False, error_message="Task timeout or failed")
    
    def _parse_completed_result(self, result: Dict, url: str) -> CrawlResult:
        """解析完成的爬取结果"""
        results = result.get("results", [])
        if results and len(results) > 0:
            crawl_data = results[0]
            return CrawlResult(
                url=url,
                success=True,
                html=crawl_data.get("html", ""),
                markdown=crawl_data.get("markdown", ""),
                cleaned_html=crawl_data.get("cleaned_html", "")
            )
        else:
            return CrawlResult(url=url, success=False, error_message="No results in completed task")


class ClassificationStrategy(ABC):
    """分类策略抽象基类"""
    
    @abstractmethod
    def classify(self, crawl_result: CrawlResult) -> str:
        """对爬取结果进行分类"""
        pass
    
    @abstractmethod
    def get_categories(self) -> List[str]:
        """获取所有可能的分类"""
        pass


class ContentBasedClassifier(ClassificationStrategy):
    """基于内容的分类器"""
    
    def __init__(self):
        self.categories = ["类型A", "类型B"]
        
        # 定义分类关键词
        self.category_keywords = {
            "类型A": [
                "管理", "行政", "办公", "部门", "委员会", "党委", "工会", 
                "人力", "财务", "企业", "宣传", "组织", "纪委", "团委"
            ],
            "类型B": [
                "生产", "技术", "工厂", "车间", "设备", "制造", "加工",
                "运输", "动力", "检测", "化工", "选矿", "采矿", "服务"
            ]
        }
    
    def classify(self, crawl_result: CrawlResult) -> str:
        """基于内容关键词进行分类"""
        if not crawl_result.success:
            return "未分类"
        
        # 合并所有文本内容
        content = " ".join([
            crawl_result.html,
            crawl_result.markdown,
            crawl_result.cleaned_html,
            crawl_result.url
        ]).lower()
        
        # 计算每个类别的匹配分数
        scores = {}
        for category, keywords in self.category_keywords.items():
            score = sum(1 for keyword in keywords if keyword in content)
            scores[category] = score
        
        # 返回得分最高的类别
        if max(scores.values()) > 0:
            return max(scores, key=scores.get)
        else:
            return "类型A"  # 默认分类
    
    def get_categories(self) -> List[str]:
        return self.categories


class TableStyleClassifier(ClassificationStrategy):
    """基于表格风格的分类器"""
    
    def __init__(self):
        self.categories = ["简单表格风格", "复杂表格风格"]
    
    def classify(self, crawl_result: CrawlResult) -> str:
        """基于表格风格进行分类"""
        if not crawl_result.success:
            return "未分类"
        
        soup = BeautifulSoup(crawl_result.html, 'html.parser')
        tables = soup.find_all('table')
        
        if not tables:
            return "简单表格风格"
        
        complex_features = 0
        simple_features = 0
        
        for table in tables:
            rows = len(table.find_all('tr'))
            colspan_usage = len(table.find_all(attrs={'colspan': True}))
            rowspan_usage = len(table.find_all(attrs={'rowspan': True}))
            
            if rows > 15 or colspan_usage > 0 or rowspan_usage > 0:
                complex_features += 1
            else:
                simple_features += 1
        
        return "复杂表格风格" if complex_features > simple_features else "简单表格风格"
    
    def get_categories(self) -> List[str]:
        return self.categories


class UniversalWebCrawlerSystem:
    """通用网页爬虫系统主类"""
    
    def __init__(self, config: CrawlConfig = None, classifier: ClassificationStrategy = None):
        self.config = config or CrawlConfig()
        self.crawler = WebCrawler(self.config)
        self.classifier = classifier or ContentBasedClassifier()
        self.link_extractor = LinkExtractor()
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('UniversalWebCrawlerSystem')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def extract_and_classify_links(self, source_url: str, output_file: str) -> None:
        """从源URL提取链接并进行分类"""
        self.logger.info(f"开始处理源URL: {source_url}")
        
        # 1. 爬取源页面
        source_result = self.crawler.crawl_url(source_url)
        if not source_result.success:
            self.logger.error(f"无法爬取源页面: {source_result.error_message}")
            return
        
        # 2. 提取链接
        links = self.link_extractor.extract_links_from_html(source_result.html, source_url)
        self.logger.info(f"从源页面提取到 {len(links)} 个链接")
        
        if not links:
            self.logger.warning("未找到任何有效链接")
            return
        
        # 3. 爬取和分类每个链接
        results = []
        for i, link in enumerate(links, 1):
            self.logger.info(f"处理第 {i}/{len(links)} 个链接: {link['text']}")
            
            # 爬取链接
            crawl_result = self.crawler.crawl_url(link['url'])
            
            # 分类
            if crawl_result.success:
                classification = self.classifier.classify(crawl_result)
                self.logger.info(f"分类结果: {classification}")
            else:
                classification = "爬取失败"
                self.logger.error(f"爬取失败: {crawl_result.error_message}")
            
            results.append({
                '单位名称': link['text'],
                '子链接': link['url'],
                '分类': classification
            })
            
            # 添加延迟避免过于频繁的请求
            time.sleep(self.config.rate_limit_delay)
        
        # 4. 保存结果
        self._save_results(results, output_file)
        self._print_summary(results)
    
    def process_existing_csv(self, input_file: str, output_file: str) -> None:
        """处理现有的CSV文件，添加分类列"""
        self.logger.info(f"开始处理CSV文件: {input_file}")
        
        try:
            df = pd.read_csv(input_file, encoding='utf-8')
            self.logger.info(f"读取到 {len(df)} 条记录")
            
            # 添加分类列（如果不存在）
            if '分类' not in df.columns:
                df['分类'] = ''
            
            # 处理每个URL
            for index, row in df.iterrows():
                unit_name = row.get('单位名称', '')
                url = row.get('子链接', '')
                
                if pd.isna(url) or not url.strip():
                    continue
                
                self.logger.info(f"处理第 {index + 1}/{len(df)} 条: {unit_name}")
                
                # 跳过已分类的记录
                if pd.notna(row.get('分类')) and row.get('分类').strip():
                    self.logger.info(f"已分类，跳过: {row.get('分类')}")
                    continue
                
                # 爬取URL
                crawl_result = self.crawler.crawl_url(url)
                
                if crawl_result.success:
                    classification = self.classifier.classify(crawl_result)
                    df.at[index, '分类'] = classification
                    self.logger.info(f"分类结果: {classification}")
                else:
                    df.at[index, '分类'] = "爬取失败"
                    self.logger.error(f"爬取失败: {crawl_result.error_message}")
                
                # 保存中间结果
                try:
                    df.to_csv(output_file, index=False, encoding='utf-8')
                except Exception as save_error:
                    self.logger.error(f"保存中间结果失败: {save_error}")
                
                # 添加延迟
                time.sleep(self.config.rate_limit_delay)
            
            # 最终保存
            df.to_csv(output_file, index=False, encoding='utf-8')
            self.logger.info(f"处理完成！结果已保存到 {output_file}")
            
            # 显示统计信息
            self._print_csv_summary(df)
            
        except Exception as e:
            self.logger.error(f"处理CSV文件时出错: {e}")
    
    def _save_results(self, results: List[Dict], output_file: str) -> None:
        """保存结果到CSV文件"""
        try:
            df = pd.DataFrame(results)
            df.to_csv(output_file, index=False, encoding='utf-8')
            self.logger.info(f"结果已保存到: {output_file}")
        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
    
    def _print_summary(self, results: List[Dict]) -> None:
        """打印处理摘要"""
        total = len(results)
        categories = {}
        
        for result in results:
            category = result['分类']
            categories[category] = categories.get(category, 0) + 1
        
        self.logger.info(f"\n📊 处理摘要:")
        self.logger.info(f"总记录数: {total}")
        for category, count in categories.items():
            percentage = (count / total) * 100 if total > 0 else 0
            self.logger.info(f"  {category}: {count} 个 ({percentage:.1f}%)")
    
    def _print_csv_summary(self, df: pd.DataFrame) -> None:
        """打印CSV处理摘要"""
        total = len(df)
        if '分类' in df.columns:
            categories = df['分类'].value_counts()
            
            self.logger.info(f"\n📊 分类统计:")
            self.logger.info(f"总记录数: {total}")
            for category, count in categories.items():
                percentage = (count / total) * 100 if total > 0 else 0
                self.logger.info(f"  {category}: {count} 个 ({percentage:.1f}%)")


def main():
    """主函数 - 演示系统使用"""
    print("🚀 通用网页爬虫和分类系统")
    print("=" * 50)
    
    # 配置系统
    config = CrawlConfig()
    
    # 选择分类策略
    print("选择分类策略:")
    print("1. 基于内容的分类")
    print("2. 基于表格风格的分类")
    
    choice = input("请输入选择 (1 或 2，默认为 1): ").strip()
    
    if choice == "2":
        classifier = TableStyleClassifier()
        print("✅ 使用表格风格分类器")
    else:
        classifier = ContentBasedClassifier()
        print("✅ 使用内容分类器")
    
    # 初始化系统
    system = UniversalWebCrawlerSystem(config, classifier)
    
    # 选择处理模式
    print("\n选择处理模式:")
    print("1. 从指定URL提取链接并分类")
    print("2. 处理现有CSV文件")
    
    mode = input("请输入选择 (1 或 2，默认为 1): ").strip()
    
    if mode == "2":
        # 处理现有CSV文件
        input_file = input("请输入输入CSV文件名 (默认: final_units_data1.csv): ").strip()
        if not input_file:
            input_file = "final_units_data1.csv"
        
        output_file = input("请输入输出CSV文件名 (默认: final_units_data.csv): ").strip()
        if not output_file:
            output_file = "final_units_data.csv"
        
        system.process_existing_csv(input_file, output_file)
    else:
        # 从URL提取链接
        source_url = input("请输入源URL (默认: http://***********/phone/20201227.html): ").strip()
        if not source_url:
            source_url = "http://***********/phone/20201227.html"
        
        output_file = input("请输入输出CSV文件名 (默认: final_units_data.csv): ").strip()
        if not output_file:
            output_file = "final_units_data.csv"
        
        system.extract_and_classify_links(source_url, output_file)


if __name__ == "__main__":
    main()
