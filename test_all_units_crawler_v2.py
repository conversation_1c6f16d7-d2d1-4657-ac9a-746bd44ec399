#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全单位电话数据爬取测试脚本 - 方法二
测试通用电话爬虫对所有单位的爬取能力
"""

import pandas as pd
import os
import sys
import time
from datetime import datetime
from universal_phone_crawler_v2 import UniversalPhoneCrawler
import logging


def test_all_units():
    """
    测试爬取所有单位的电话数据
    """
    print("🚀 开始全单位电话数据爬取测试...")
    print("=" * 80)
    
    # 创建爬虫实例
    crawler = UniversalPhoneCrawler(log_level=logging.INFO)
    
    # 读取所有单位列表
    try:
        df = pd.read_csv('final_units_data.csv', encoding='utf-8')
        all_units = df['单位名称'].tolist()
        print(f"📋 发现 {len(all_units)} 个单位需要爬取")
        print(f"单位列表: {', '.join(all_units[:5])}{'...' if len(all_units) > 5 else ''}")
    except Exception as e:
        print(f"❌ 无法读取单位列表: {e}")
        return
    
    # 创建结果统计
    results = {
        'success_units': [],
        'failed_units': [],
        'total_records': 0,
        'unit_records': {},
        'errors': {}
    }
    
    # 创建汇总数据列表
    all_phone_data = []
    
    print(f"\n🎯 开始逐个爬取单位数据...")
    print("-" * 80)
    
    # 逐个爬取每个单位
    for i, unit_name in enumerate(all_units, 1):
        print(f"\n[{i}/{len(all_units)}] 🏢 正在爬取: {unit_name}")
        
        try:
            # 爬取单位数据
            start_time = time.time()
            phone_data = crawler.crawl_unit_phones(unit_name)
            end_time = time.time()
            
            if phone_data:
                # 成功获取数据
                record_count = len(phone_data)
                results['success_units'].append(unit_name)
                results['total_records'] += record_count
                results['unit_records'][unit_name] = record_count
                
                # 添加到汇总数据
                all_phone_data.extend(phone_data)
                
                # 保存单个单位的数据
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                unit_filename = f"{unit_name}_phone_data_v2_{timestamp}.csv"
                crawler.save_to_csv(phone_data, unit_filename)
                
                print(f"  ✅ 成功: {record_count} 条记录 (耗时: {end_time-start_time:.1f}秒)")
                print(f"  💾 已保存: {unit_filename}")
                
                # 显示前3条数据作为示例
                if record_count > 0:
                    print(f"  📋 数据示例:")
                    for j, record in enumerate(phone_data[:3]):
                        print(f"    {j+1}. {record['电话号码']} | {record['电话用户名']} | {record['单位名称']}")
                    if record_count > 3:
                        print(f"    ... 还有 {record_count - 3} 条记录")
                
            else:
                # 未获取到数据
                results['failed_units'].append(unit_name)
                results['errors'][unit_name] = "未获取到任何数据"
                print(f"  ⚠️ 警告: 未获取到数据")
                
        except Exception as e:
            # 爬取失败
            results['failed_units'].append(unit_name)
            results['errors'][unit_name] = str(e)
            print(f"  ❌ 失败: {e}")
        
        # 添加延迟避免过于频繁的请求
        if i < len(all_units):
            time.sleep(1)
    
    print("\n" + "=" * 80)
    print("📊 爬取完成！生成统计报告...")
    
    # 生成详细统计报告
    generate_summary_report(results, all_phone_data)
    
    # 保存汇总数据
    if all_phone_data:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_filename = f"all_units_phone_data_v2_{timestamp}.csv"
        crawler.save_to_csv(all_phone_data, summary_filename)
        print(f"\n💾 汇总数据已保存: {summary_filename}")
    
    return results


def generate_summary_report(results, all_phone_data):
    """
    生成详细的统计报告
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"all_units_crawl_report_v2_{timestamp}.txt"
    
    print(f"\n📈 统计报告:")
    print(f"  成功单位: {len(results['success_units'])} 个")
    print(f"  失败单位: {len(results['failed_units'])} 个")
    print(f"  总记录数: {results['total_records']} 条")
    print(f"  成功率: {len(results['success_units'])/(len(results['success_units'])+len(results['failed_units']))*100:.1f}%")
    
    # 写入详细报告文件
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("全单位电话数据爬取测试报告 - 方法二\n")
        f.write("=" * 60 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("总体统计:\n")
        f.write(f"  总单位数: {len(results['success_units']) + len(results['failed_units'])}\n")
        f.write(f"  成功单位: {len(results['success_units'])} 个\n")
        f.write(f"  失败单位: {len(results['failed_units'])} 个\n")
        f.write(f"  总记录数: {results['total_records']} 条\n")
        f.write(f"  成功率: {len(results['success_units'])/(len(results['success_units'])+len(results['failed_units']))*100:.1f}%\n\n")
        
        f.write("成功单位详情:\n")
        f.write("-" * 40 + "\n")
        for unit in results['success_units']:
            record_count = results['unit_records'].get(unit, 0)
            f.write(f"  {unit}: {record_count} 条记录\n")
        
        if results['failed_units']:
            f.write(f"\n失败单位详情:\n")
            f.write("-" * 40 + "\n")
            for unit in results['failed_units']:
                error = results['errors'].get(unit, "未知错误")
                f.write(f"  {unit}: {error}\n")
        
        # 数据质量分析
        if all_phone_data:
            f.write(f"\n数据质量分析:\n")
            f.write("-" * 40 + "\n")
            
            # 统计电话号码格式
            valid_7_digit = sum(1 for record in all_phone_data if len(record['电话号码'].replace('*', '')) == 7)
            valid_11_digit = sum(1 for record in all_phone_data if len(record['电话号码'].replace('*', '')) == 11)
            duplicate_marked = sum(1 for record in all_phone_data if '*' in record['电话号码'])
            
            f.write(f"  7位号码: {valid_7_digit} 个\n")
            f.write(f"  11位号码: {valid_11_digit} 个\n")
            f.write(f"  重复标记: {duplicate_marked} 个\n")
            
            # 统计单位分布
            unit_distribution = {}
            for record in all_phone_data:
                unit = record['单位名称']
                unit_distribution[unit] = unit_distribution.get(unit, 0) + 1
            
            f.write(f"\n单位分布 (前10名):\n")
            sorted_units = sorted(unit_distribution.items(), key=lambda x: x[1], reverse=True)
            for unit, count in sorted_units[:10]:
                f.write(f"  {unit}: {count} 条\n")
    
    print(f"📄 详细报告已保存: {report_filename}")


def analyze_crawl_results():
    """
    分析爬取结果，与标准程序对比
    """
    print("\n🔍 分析爬取结果...")
    
    # 查找最新的汇总文件
    csv_files = [f for f in os.listdir('.') if f.startswith('all_units_phone_data_v2_') and f.endswith('.csv')]
    if not csv_files:
        print("❌ 未找到汇总数据文件")
        return
    
    latest_file = max(csv_files, key=lambda x: os.path.getctime(x))
    print(f"📊 分析文件: {latest_file}")
    
    try:
        df = pd.read_csv(latest_file, encoding='utf-8')
        
        print(f"\n📈 数据分析结果:")
        print(f"  总记录数: {len(df)}")
        print(f"  唯一单位数: {df['单位名称'].nunique()}")
        print(f"  唯一电话数: {df['电话号码'].nunique()}")
        
        # 分析重复电话
        duplicate_phones = df[df['电话号码'].str.contains('\*', na=False)]
        print(f"  重复电话标记: {len(duplicate_phones)} 个")
        
        # 分析单位分布
        unit_counts = df['单位名称'].value_counts()
        print(f"\n📋 单位记录数分布 (前5名):")
        for unit, count in unit_counts.head().items():
            print(f"    {unit}: {count} 条")
        
        # 分析电话号码格式
        phone_lengths = df['电话号码'].str.replace('*', '').str.len().value_counts().sort_index()
        print(f"\n📞 电话号码长度分布:")
        for length, count in phone_lengths.items():
            print(f"    {length}位: {count} 个")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")


def main():
    """
    主程序入口
    """
    print("🧪 全单位电话数据爬取测试 - 方法二")
    print("=" * 80)
    
    # 检查必要文件
    if not os.path.exists('final_units_data.csv'):
        print("❌ 未找到 final_units_data.csv 文件")
        return
    
    if not os.path.exists('universal_phone_crawler_v2.py'):
        print("❌ 未找到 universal_phone_crawler_v2.py 文件")
        return
    
    # 询问用户是否继续
    response = input("\n⚠️ 即将开始爬取所有单位数据，这可能需要较长时间。是否继续？(y/N): ")
    if response.lower() != 'y':
        print("❌ 用户取消操作")
        return
    
    # 开始测试
    start_time = time.time()
    results = test_all_units()
    end_time = time.time()
    
    print(f"\n⏱️ 总耗时: {end_time - start_time:.1f} 秒")
    
    # 分析结果
    analyze_crawl_results()
    
    print(f"\n🎉 全单位爬取测试完成！")


if __name__ == "__main__":
    main()
