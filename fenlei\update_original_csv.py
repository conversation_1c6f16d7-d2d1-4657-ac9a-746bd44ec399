#!/usr/bin/env python3
"""
更新原始CSV文件，添加分类列
"""
import pandas as pd
import shutil

def update_original_csv():
    """
    将分类结果复制到原始CSV文件
    """
    try:
        # 备份原始文件
        shutil.copy('final_units_data.csv', 'final_units_data_backup.csv')
        print("✅ 已备份原始文件为: final_units_data_backup.csv")
        
        # 读取分类结果
        classified_df = pd.read_csv('final_units_data_classified.csv', encoding='utf-8')
        print(f"✅ 读取分类结果: {len(classified_df)} 条记录")
        
        # 保存到原始文件
        classified_df.to_csv('final_units_data.csv', index=False, encoding='utf-8')
        print("✅ 已更新原始文件: final_units_data.csv")
        
        # 验证结果
        verify_df = pd.read_csv('final_units_data.csv', encoding='utf-8')
        print(f"✅ 验证结果: {len(verify_df)} 条记录，列名: {list(verify_df.columns)}")
        
        # 显示分类统计
        classification_counts = verify_df['分类'].value_counts()
        print("\n📊 最终分类统计:")
        for category, count in classification_counts.items():
            print(f"  {category}: {count} 个")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")

if __name__ == "__main__":
    update_original_csv()
