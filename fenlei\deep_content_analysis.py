#!/usr/bin/env python3
"""
深度内容分析 - 查找实际的电话号码显示区域
"""
from table_border_classifier import CrawlConfig, WebCrawler
from bs4 import BeautifulSoup
import re

def deep_analyze_pages():
    """深度分析页面内容"""
    config = CrawlConfig()
    crawler = WebCrawler(config)
    
    sample_urls = [
        ("http://172.18.1.16/phone/机关部室/xzgzb.html", "行政工作部"),
        ("http://172.18.1.16/phone/主要生产单位/化工公司/化工公司.html", "化工公司")
    ]
    
    for url, name in sample_urls:
        print(f"\n{'='*80}")
        print(f"深度分析: {name}")
        print(f"URL: {url}")
        print('='*80)
        
        result = crawler.crawl_url(url)
        if not result.success:
            print(f"❌ 爬取失败: {result.error_message}")
            continue
        
        soup = BeautifulSoup(result.html, 'html.parser')
        
        # 查找所有包含电话号码的区域
        phone_areas = find_phone_content_areas(soup)
        
        print(f"找到 {len(phone_areas)} 个电话内容区域")
        
        for i, area in enumerate(phone_areas):
            print(f"\n📞 电话区域 {i+1}:")
            analyze_phone_area(area)

def find_phone_content_areas(soup):
    """查找所有包含电话号码的内容区域"""
    phone_areas = []
    
    # 查找所有包含7位数字电话号码的元素
    phone_pattern = r'77\d{5}'
    
    # 检查所有元素
    for element in soup.find_all():
        text = element.get_text()
        if re.search(phone_pattern, text):
            # 检查这个元素是否包含多个电话号码
            phone_matches = re.findall(phone_pattern, text)
            if len(phone_matches) >= 3:  # 至少包含3个电话号码
                phone_areas.append(element)
    
    # 去重 - 移除嵌套的元素
    filtered_areas = []
    for area in phone_areas:
        is_nested = False
        for other in phone_areas:
            if area != other and area in other.descendants:
                is_nested = True
                break
        if not is_nested:
            filtered_areas.append(area)
    
    return filtered_areas

def analyze_phone_area(area):
    """分析电话号码显示区域"""
    print(f"元素类型: {area.name}")
    print(f"元素属性: {dict(area.attrs)}")
    
    # 提取电话号码
    text = area.get_text()
    phone_pattern = r'77\d{5}'
    phones = re.findall(phone_pattern, text)
    print(f"包含电话号码数量: {len(phones)}")
    
    # 分析结构
    if area.name == 'table':
        analyze_table_phone_layout(area)
    else:
        # 查找内部的表格
        tables = area.find_all('table')
        if tables:
            print(f"包含 {len(tables)} 个表格")
            for i, table in enumerate(tables):
                print(f"\n  表格 {i+1}:")
                analyze_table_phone_layout(table, indent="    ")
        else:
            print("不包含表格结构")
            analyze_non_table_layout(area)

def analyze_table_phone_layout(table, indent=""):
    """分析表格中的电话号码布局"""
    rows = table.find_all('tr')
    print(f"{indent}表格行数: {len(rows)}")
    
    if not rows:
        return
    
    # 分析每行的结构
    phone_pattern = r'77\d{5}'
    table_data = []
    
    for i, row in enumerate(rows):
        cells = row.find_all(['td', 'th'])
        row_data = []
        for cell in cells:
            cell_text = cell.get_text(strip=True)
            has_phone = bool(re.search(phone_pattern, cell_text))
            row_data.append({
                'text': cell_text[:30] + '...' if len(cell_text) > 30 else cell_text,
                'has_phone': has_phone,
                'colspan': cell.get('colspan', '1'),
                'rowspan': cell.get('rowspan', '1')
            })
        table_data.append(row_data)
    
    # 显示表格结构
    print(f"{indent}表格结构分析:")
    for i, row_data in enumerate(table_data[:10]):  # 只显示前10行
        print(f"{indent}  行{i+1}: {len(row_data)}列")
        for j, cell in enumerate(row_data):
            if cell['has_phone']:
                print(f"{indent}    列{j+1}: 📞 {cell['text']}")
            elif cell['text']:
                print(f"{indent}    列{j+1}: {cell['text']}")
    
    # 判断布局类型
    determine_layout_type(table_data, indent)

def analyze_non_table_layout(area, indent=""):
    """分析非表格布局"""
    print(f"{indent}非表格布局分析:")
    
    # 查找电话号码的显示模式
    text = area.get_text()
    lines = text.split('\n')
    phone_lines = [line.strip() for line in lines if re.search(r'77\d{5}', line)]
    
    print(f"{indent}包含电话的行数: {len(phone_lines)}")
    for i, line in enumerate(phone_lines[:5]):
        print(f"{indent}  {i+1}: {line}")

def determine_layout_type(table_data, indent=""):
    """判断布局类型"""
    if not table_data:
        return
    
    # 统计多列行的数量
    multi_column_rows = 0
    max_columns = 0
    
    for row_data in table_data:
        if len(row_data) > 2:
            multi_column_rows += 1
        max_columns = max(max_columns, len(row_data))
    
    total_rows = len(table_data)
    multi_column_ratio = multi_column_rows / total_rows if total_rows > 0 else 0
    
    print(f"{indent}布局特征:")
    print(f"{indent}  最大列数: {max_columns}")
    print(f"{indent}  多列行比例: {multi_column_ratio:.2f} ({multi_column_rows}/{total_rows})")
    
    # 判断类型
    if max_columns > 3 and multi_column_ratio > 0.3:
        layout_type = "复杂表格布局 (有表格线)"
    else:
        layout_type = "简单列表布局 (无表格线)"
    
    print(f"{indent}🎯 布局判断: {layout_type}")
    
    return layout_type

if __name__ == "__main__":
    deep_analyze_pages()
