#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主爬虫程序
整合所有模块，实现完整的电话信息爬取流程
"""

import asyncio
import csv
import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path

# 导入自定义模块
from base_crawler import BaseCrawler, CrawlConfig
from phone_extractor import PhoneExtractor, PhoneRecord
from csv_processor import CSVProcessor, UnitRecord


class MainCrawler:
    """
    主爬虫类
    
    功能：
    1. 爬取主页面电话信息
    2. 逐行处理CSV文件中的单位链接
    3. 根据分类使用不同的提取方法
    4. 汇总和保存所有结果
    """
    
    def __init__(self, config: CrawlConfig = None):
        """初始化主爬虫"""
        self.config = config or CrawlConfig()
        self.logger = self._setup_logger()
        
        # 初始化组件
        self.crawler = BaseCrawler(self.config)
        self.extractor = PhoneExtractor()
        self.csv_processor = CSVProcessor()
        
        # 存储所有提取的电话记录
        self.all_phone_records: List[PhoneRecord] = []
        
        # 统计信息
        self.stats = {
            'total_units': 0,
            'successful_units': 0,
            'failed_units': 0,
            'total_phones': 0,
            'main_page_phones': 0,
            'table_style_phones': 0,
            'text_style_phones': 0,
            'start_time': None,
            'end_time': None
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        if not logger.handlers:
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件处理器
            file_handler = logging.FileHandler(
                f'main_crawler_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log',
                encoding='utf-8'
            )
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            logger.setLevel(logging.INFO)
        return logger
    
    async def run_complete_crawl(self):
        """运行完整的爬取流程"""
        self.stats['start_time'] = datetime.now()
        
        print("🚀 电话信息爬虫系统启动")
        print("=" * 60)
        
        try:
            # 步骤1：爬取主页面电话信息
            await self._crawl_main_page()
            
            # 步骤2：加载CSV数据
            if not self._load_csv_data():
                return
            
            # 步骤3：逐行处理CSV文件
            await self._process_csv_units()
            
            # 步骤4：保存结果
            self._save_all_results()
            
            # 步骤5：显示统计信息
            self._show_final_stats()
            
        except Exception as e:
            self.logger.error(f"❌ 爬取过程中发生错误: {e}")
        finally:
            self.stats['end_time'] = datetime.now()
    
    async def _crawl_main_page(self):
        """爬取主页面电话信息"""
        main_url = "http://172.18.1.16/phone/20201227.html"
        
        self.logger.info("📋 步骤1: 爬取主页面电话信息")
        self.logger.info(f"🌐 目标URL: {main_url}")
        
        try:
            # 爬取主页面
            html_content = await self.crawler.crawl_url(main_url)
            if not html_content:
                self.logger.error("❌ 主页面爬取失败")
                return
            
            # 提取电话信息
            main_records = self.extractor.extract_phone_info(
                html_content, 
                "矿领导", 
                "auto"
            )
            
            if main_records:
                self.all_phone_records.extend(main_records)
                self.stats['main_page_phones'] = len(main_records)
                self.logger.info(f"✅ 主页面提取到 {len(main_records)} 条电话记录")
                
                # 显示主页面结果
                print(f"\n📋 主页面电话信息 ({len(main_records)} 条):")
                for i, record in enumerate(main_records, 1):
                    print(f"  {i:2d}. {record.phone} - {record.name} ({record.unit})")
            else:
                self.logger.warning("⚠️ 主页面未提取到电话信息")
                
        except Exception as e:
            self.logger.error(f"❌ 主页面爬取异常: {e}")
    
    def _load_csv_data(self) -> bool:
        """加载CSV数据"""
        self.logger.info("📂 步骤2: 加载CSV数据")
        
        if not self.csv_processor.load_data():
            self.logger.error("❌ CSV数据加载失败")
            return False
        
        # 验证数据
        if not self.csv_processor.validate_data():
            self.logger.warning("⚠️ CSV数据验证有问题，但继续处理")
        
        # 显示统计信息
        summary = self.csv_processor.get_summary()
        self.stats['total_units'] = summary['total_rows']
        
        print(f"\n📊 CSV数据统计:")
        print(f"  总单位数: {summary['total_rows']}")
        for classification, count in summary['classification_counts'].items():
            print(f"  {classification}: {count} 个单位")
        
        return True
    
    async def _process_csv_units(self):
        """逐行处理CSV文件中的单位"""
        self.logger.info("🔄 步骤3: 处理CSV文件中的单位")

        processed_count = 0

        # 迭代所有单位记录
        for record in self.csv_processor.iter_all_records():
            # 跳过爬取失败的记录
            if record.classification == "爬取失败":
                self.logger.info(f"⏭️ 跳过爬取失败的单位: {record.unit_name}")
                continue

            processed_count += 1

            print(f"\n📋 处理第 {processed_count}/{self.stats['total_units']} 个单位:")
            print(f"  单位: {record.unit_name}")
            print(f"  分类: {record.classification}")
            print(f"  链接: {record.sub_link}")

            try:
                # 爬取单位页面
                html_content = await self.crawler.crawl_url(record.sub_link)
                if not html_content:
                    self.logger.error(f"❌ 爬取失败: {record.unit_name}")
                    self.stats['failed_units'] += 1
                    continue

                # 根据分类提取电话信息
                unit_records = self.extractor.extract_phone_info(
                    html_content,
                    record.unit_name,
                    record.classification
                )

                if unit_records:
                    self.all_phone_records.extend(unit_records)
                    self.stats['successful_units'] += 1

                    # 更新分类统计
                    if record.classification == "有表格线":
                        self.stats['table_style_phones'] += len(unit_records)
                    else:
                        self.stats['text_style_phones'] += len(unit_records)

                    print(f"  ✅ 提取到 {len(unit_records)} 条电话记录")

                    # 显示提取的记录（限制显示数量）
                    for i, phone_record in enumerate(unit_records[:5], 1):
                        print(f"    {i}. {phone_record.phone} - {phone_record.name}")
                    if len(unit_records) > 5:
                        print(f"    ... 还有 {len(unit_records) - 5} 条记录")
                else:
                    self.logger.warning(f"⚠️ 未提取到电话信息: {record.unit_name}")
                    self.stats['failed_units'] += 1

                # 添加延迟，避免请求过快
                await asyncio.sleep(self.config.rate_limit_delay)

            except Exception as e:
                self.logger.error(f"❌ 处理单位异常: {record.unit_name} - {e}")
                self.stats['failed_units'] += 1

        self.stats['total_phones'] = len(self.all_phone_records)
    
    def _save_all_results(self):
        """保存所有结果"""
        self.logger.info("💾 步骤4: 保存结果")
        
        if not self.all_phone_records:
            self.logger.warning("⚠️ 没有数据可保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存CSV文件
        csv_filename = f"所有电话信息_{timestamp}.csv"
        self._save_to_csv(csv_filename)
        
        # 保存JSON文件
        json_filename = f"所有电话信息_{timestamp}.json"
        self._save_to_json(json_filename)
        
        # 保存分类统计
        stats_filename = f"爬取统计_{timestamp}.json"
        self._save_stats(stats_filename)
        
        print(f"\n💾 结果已保存:")
        print(f"  📄 CSV文件: {csv_filename}")
        print(f"  📄 JSON文件: {json_filename}")
        print(f"  📊 统计文件: {stats_filename}")
    
    def _save_to_csv(self, filename: str):
        """保存为CSV文件"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['电话号码', '单位名称', '电话用户名', '提取方法']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for record in self.all_phone_records:
                    writer.writerow({
                        '电话号码': record.phone,
                        '单位名称': record.unit,
                        '电话用户名': record.name,
                        '提取方法': record.extraction_method
                    })
            
            self.logger.info(f"✅ CSV文件保存成功: {filename}")
        except Exception as e:
            self.logger.error(f"❌ CSV文件保存失败: {e}")
    
    def _save_to_json(self, filename: str):
        """保存为JSON文件"""
        try:
            data = []
            for record in self.all_phone_records:
                data.append({
                    '电话号码': record.phone,
                    '单位名称': record.unit,
                    '电话用户名': record.name,
                    '提取方法': record.extraction_method
                })
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ JSON文件保存成功: {filename}")
        except Exception as e:
            self.logger.error(f"❌ JSON文件保存失败: {e}")
    
    def _save_stats(self, filename: str):
        """保存统计信息"""
        try:
            # 计算运行时间
            if self.stats['start_time'] and self.stats['end_time']:
                duration = self.stats['end_time'] - self.stats['start_time']
                self.stats['duration_seconds'] = duration.total_seconds()
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"✅ 统计文件保存成功: {filename}")
        except Exception as e:
            self.logger.error(f"❌ 统计文件保存失败: {e}")
    
    def _show_final_stats(self):
        """显示最终统计信息"""
        print(f"\n🎉 爬取完成！最终统计:")
        print("=" * 60)
        print(f"📊 处理单位: {self.stats['total_units']} 个")
        print(f"✅ 成功单位: {self.stats['successful_units']} 个")
        print(f"❌ 失败单位: {self.stats['failed_units']} 个")
        print(f"📞 总电话数: {self.stats['total_phones']} 条")
        print(f"  - 主页面: {self.stats['main_page_phones']} 条")
        print(f"  - 表格样式: {self.stats['table_style_phones']} 条")
        print(f"  - 文本样式: {self.stats['text_style_phones']} 条")
        
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            print(f"⏱️ 总耗时: {duration}")
        
        print("=" * 60)


async def main():
    """主函数"""
    # 创建爬虫配置
    config = CrawlConfig(
        api_base_url="http://172.18.151.239:11235",
        api_token="123456",
        max_retries=15,
        retry_delay=3.0,
        request_timeout=60,
        rate_limit_delay=1.0
    )
    
    # 创建主爬虫实例
    main_crawler = MainCrawler(config)
    
    # 运行完整爬取流程
    await main_crawler.run_complete_crawl()


if __name__ == "__main__":
    """程序入口"""
    print("🕷️ 模块化电话信息爬虫系统")
    print("基于crawl4ai的通用电话信息提取工具")
    print("支持有表格线和无表格线两种网页样式")
    print("=" * 60)
    
    # 运行主程序
    asyncio.run(main())
