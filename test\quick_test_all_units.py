#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速全单位电话数据爬取测试
"""

import pandas as pd
import os
import time
from datetime import datetime
from universal_phone_crawler_v2 import UniversalPhoneCrawler
import logging

def main():
    print("🧪 快速全单位电话数据爬取测试")
    print("=" * 60)
    
    # 检查文件
    if not os.path.exists('final_units_data.csv'):
        print("❌ 未找到 final_units_data.csv")
        return
    
    if not os.path.exists('universal_phone_crawler_v2.py'):
        print("❌ 未找到 universal_phone_crawler_v2.py")
        return
    
    print("✅ 文件检查通过")
    
    # 读取单位列表
    try:
        df = pd.read_csv('final_units_data.csv', encoding='utf-8')
        units = df['单位名称'].tolist()
        print(f"📋 发现 {len(units)} 个单位: {units}")
    except Exception as e:
        print(f"❌ 读取单位列表失败: {e}")
        return
    
    # 创建爬虫
    print("🔧 创建爬虫实例...")
    crawler = UniversalPhoneCrawler(log_level=logging.ERROR)  # 只显示错误
    
    # 统计结果
    results = {
        'success': [],
        'failed': [],
        'total_records': 0
    }
    
    all_data = []
    
    # 逐个爬取
    for i, unit in enumerate(units, 1):
        print(f"\n[{i}/{len(units)}] 🏢 爬取: {unit}")
        
        try:
            start_time = time.time()
            data = crawler.crawl_unit_phones(unit)
            end_time = time.time()
            
            if data:
                count = len(data)
                results['success'].append(unit)
                results['total_records'] += count
                all_data.extend(data)
                
                print(f"  ✅ 成功: {count} 条记录 ({end_time-start_time:.1f}秒)")
                
                # 显示前2条示例
                for j, record in enumerate(data[:2]):
                    print(f"    {j+1}. {record['电话号码']} | {record['电话用户名']} | {record['单位名称']}")
                if count > 2:
                    print(f"    ... 还有 {count-2} 条")
            else:
                results['failed'].append(unit)
                print(f"  ⚠️ 无数据")
                
        except Exception as e:
            results['failed'].append(unit)
            print(f"  ❌ 失败: {e}")
        
        # 短暂延迟
        time.sleep(0.3)
    
    # 生成报告
    print("\n" + "=" * 60)
    print("📊 爬取完成统计:")
    print(f"  成功单位: {len(results['success'])} 个")
    print(f"  失败单位: {len(results['failed'])} 个")
    print(f"  总记录数: {results['total_records']} 条")
    
    if results['success']:
        print(f"\n✅ 成功单位: {', '.join(results['success'])}")
    
    if results['failed']:
        print(f"\n❌ 失败单位: {', '.join(results['failed'])}")
    
    # 保存汇总数据
    if all_data:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"all_units_summary_v2_{timestamp}.csv"
        
        try:
            df_result = pd.DataFrame(all_data)
            df_result.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"\n💾 汇总数据已保存: {filename}")
            
            # 数据质量分析
            print(f"\n📈 数据质量分析:")
            print(f"  总记录数: {len(df_result)}")
            print(f"  唯一单位: {df_result['单位名称'].nunique()}")
            print(f"  唯一电话: {df_result['电话号码'].nunique()}")
            
            # 重复标记统计
            duplicate_count = df_result['电话号码'].str.contains('\*', na=False).sum()
            print(f"  重复标记: {duplicate_count} 个")
            
            # 电话长度分布
            phone_lengths = df_result['电话号码'].str.replace('*', '').str.len().value_counts().sort_index()
            print(f"  电话长度分布: {dict(phone_lengths)}")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    print(f"\n🎉 测试完成！")

if __name__ == "__main__":
    main()
