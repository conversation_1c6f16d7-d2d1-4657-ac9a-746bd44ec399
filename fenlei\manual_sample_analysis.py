#!/usr/bin/env python3
"""
手动分析样本页面，理解实际的HTML结构差异
"""
from table_border_classifier import CrawlConfig, WebCrawler
from bs4 import BeautifulSoup
import re

def analyze_specific_pages():
    """分析特定页面的HTML结构"""
    config = CrawlConfig()
    crawler = WebCrawler(config)
    
    # 根据您的图片示例，我们需要找到两种不同的页面类型
    sample_urls = [
        ("http://172.18.1.16/phone/机关部室/xzgzb.html", "行政工作部 (应该是第一类-无表格线)"),
        ("http://172.18.1.16/phone/主要生产单位/化工公司/化工公司.html", "化工公司 (应该是第二类-有表格线)"),
        ("http://172.18.1.16/phone/机关部室/kjw.html", "矿纪委"),
        ("http://172.18.1.16/phone/主要生产单位/采矿场/ckc.html", "采矿场")
    ]
    
    for url, name in sample_urls:
        print(f"\n{'='*80}")
        print(f"分析页面: {name}")
        print(f"URL: {url}")
        print('='*80)
        
        result = crawler.crawl_url(url)
        if not result.success:
            print(f"❌ 爬取失败: {result.error_message}")
            continue
        
        soup = BeautifulSoup(result.html, 'html.parser')
        
        # 查找主要内容区域
        main_content = find_main_content_area(soup)
        if main_content:
            analyze_content_structure(main_content, name)
        else:
            print("未找到主要内容区域")

def find_main_content_area(soup):
    """查找主要内容区域"""
    # 查找包含电话号码信息的主要区域
    # 通常这些信息在特定的表格或div中
    
    # 方法1: 查找包含电话号码的表格
    tables = soup.find_all('table')
    for table in tables:
        text = table.get_text()
        if '电话' in text or '7719' in text or '7716' in text or '7717' in text or '7718' in text:
            return table
    
    # 方法2: 查找包含电话信息的div
    divs = soup.find_all('div')
    for div in divs:
        text = div.get_text()
        if '电话' in text and ('7719' in text or '7716' in text or '7717' in text or '7718' in text):
            return div
    
    return None

def analyze_content_structure(content, page_name):
    """分析内容结构"""
    print(f"\n📋 内容结构分析:")
    
    # 检查是否为表格
    if content.name == 'table':
        print("✓ 主要内容在表格中")
        analyze_table_structure(content)
    else:
        print("✓ 主要内容在其他元素中")
        # 查找内部的表格
        tables = content.find_all('table')
        if tables:
            print(f"  包含 {len(tables)} 个子表格")
            for i, table in enumerate(tables[:3]):  # 只分析前3个
                print(f"\n  子表格 {i+1}:")
                analyze_table_structure(table, indent="    ")
        else:
            print("  不包含表格")

def analyze_table_structure(table, indent=""):
    """分析表格结构"""
    rows = table.find_all('tr')
    print(f"{indent}行数: {len(rows)}")
    
    if not rows:
        return
    
    # 分析列结构
    column_counts = []
    for row in rows:
        cells = row.find_all(['td', 'th'])
        if cells:
            column_counts.append(len(cells))
    
    if column_counts:
        print(f"{indent}列数范围: {min(column_counts)} - {max(column_counts)}")
        print(f"{indent}平均列数: {sum(column_counts)/len(column_counts):.1f}")
    
    # 检查表格属性
    border = table.get('border', 'None')
    cellpadding = table.get('cellpadding', 'None')
    cellspacing = table.get('cellspacing', 'None')
    style = table.get('style', 'None')
    class_attr = table.get('class', 'None')
    
    print(f"{indent}border: {border}")
    print(f"{indent}cellpadding: {cellpadding}")
    print(f"{indent}cellspacing: {cellspacing}")
    print(f"{indent}style: {style}")
    print(f"{indent}class: {class_attr}")
    
    # 分析内容模式
    analyze_content_pattern(table, indent)

def analyze_content_pattern(table, indent=""):
    """分析内容模式"""
    rows = table.find_all('tr')
    if len(rows) < 2:
        return
    
    print(f"{indent}内容模式分析:")
    
    # 检查前几行的内容
    for i, row in enumerate(rows[:5]):
        cells = row.find_all(['td', 'th'])
        if cells:
            cell_texts = [cell.get_text(strip=True) for cell in cells]
            non_empty = [text for text in cell_texts if text]
            print(f"{indent}  行{i+1}: {len(non_empty)}个非空单元格 - {non_empty[:3]}...")
    
    # 检查是否有明显的表格结构
    has_headers = any(row.find('th') for row in rows)
    print(f"{indent}有表头: {has_headers}")
    
    # 检查是否有多列数据
    multi_column_rows = 0
    for row in rows:
        cells = row.find_all(['td', 'th'])
        if len(cells) > 2:
            multi_column_rows += 1
    
    print(f"{indent}多列行数: {multi_column_rows}/{len(rows)}")
    
    # 检查内容是否像列表还是表格
    if multi_column_rows > len(rows) * 0.5:
        print(f"{indent}🔍 判断: 表格型布局")
    else:
        print(f"{indent}🔍 判断: 列表型布局")

if __name__ == "__main__":
    analyze_specific_pages()
