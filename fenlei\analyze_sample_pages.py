#!/usr/bin/env python3
"""
分析样本页面的HTML结构
"""
from table_border_classifier import CrawlConfig, WebCrawler
from bs4 import BeautifulSoup

def analyze_page_structure(url: str, page_name: str):
    """分析单个页面的HTML结构"""
    print(f"\n🔍 分析页面: {page_name}")
    print(f"URL: {url}")
    print("-" * 60)
    
    config = CrawlConfig()
    crawler = WebCrawler(config)
    
    result = crawler.crawl_url(url)
    if not result.success:
        print(f"❌ 爬取失败: {result.error_message}")
        return
    
    soup = BeautifulSoup(result.html, 'html.parser')
    
    # 查找所有表格
    tables = soup.find_all('table')
    print(f"表格数量: {len(tables)}")
    
    for i, table in enumerate(tables):
        print(f"\n表格 {i+1}:")
        
        # 检查表格属性
        border = table.get('border')
        style = table.get('style', '')
        class_attr = table.get('class', [])
        
        print(f"  border属性: {border}")
        print(f"  style属性: {style}")
        print(f"  class属性: {class_attr}")
        
        # 检查表格内容
        rows = table.find_all('tr')
        print(f"  行数: {len(rows)}")
        
        if rows:
            first_row = rows[0]
            cells = first_row.find_all(['td', 'th'])
            print(f"  第一行单元格数: {len(cells)}")
            
            if cells:
                first_cell = cells[0]
                cell_style = first_cell.get('style', '')
                print(f"  第一个单元格style: {cell_style}")
        
        # 输出表格的HTML片段
        table_html = str(table)[:500]
        print(f"  HTML片段: {table_html}...")

def main():
    """主函数"""
    print("🔍 分析样本页面HTML结构")
    print("=" * 60)
    
    # 分析几个代表性页面
    sample_pages = [
        ("http://172.18.1.16/phone/机关部室/xzgzb.html", "行政工作部"),
        ("http://172.18.1.16/phone/主要生产单位/化工公司/化工公司.html", "化工公司"),
        ("http://172.18.1.16/phone/辅助生产企业/检化中心.html", "检化中心")
    ]
    
    for url, name in sample_pages:
        analyze_page_structure(url, name)

if __name__ == "__main__":
    main()
